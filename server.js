const express = require('express');
const path = require('path');
const app = express();
const PORT = process.env.PORT || 4500;


app.use(express.static(path.join(__dirname, 'src')));
app.use('/public', express.static(path.join(__dirname, 'public'))); 


app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'src', 'index.html'));
});

const server = app.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);

  if (process.env.NODE_ENV !== 'production') {
    const bs = require('browser-sync').create();
    

    bs.init({
      proxy: `http://localhost:${PORT}`,
      port: 4501,  
      ui: { port: 4502 }, 
      open: false,
      notify: false,
      files: [
        'src/**/*.html',
        'src/**/*.css',
        'src/**/*.js',
        'src/**/*.json'
      ],
      watchOptions: {
        ignoreInitial: false,
        usePolling: true, 
        interval: 500,
        binaryInterval: 1000
      }
    });
  }
});