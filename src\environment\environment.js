document.addEventListener('DOMContentLoaded', () => {
    const container = document.querySelector('.scroll-container');
    const sections = document.querySelectorAll('.scroll-section');
    const environmentContainer = document.querySelector('.environment-container');
    let currentSection = 0;
    let isAnimating = false;
    let startY = 0;
    let startX = 0;
    let lastScrollTime = 0;
    let hasStartedScrolling = false;

    // Add initial animation to the main text in the environment container
    const mainText = environmentContainer.querySelector('.main-text');
    if (mainText) {
        // Apply initial animation to make it appear when the page loads
        mainText.style.opacity = '0';
        mainText.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            mainText.style.transition = 'opacity 0.8s ease-out, transform 0.8s ease-out';
            mainText.style.opacity = '1';
            mainText.style.transform = 'translateY(0)';
        }, 100);
    }

    // Initialize first section as active
    sections[0].classList.add('active');

    // Hide scroll sections initially
    container.style.display = 'none';

    function startScrollSections() {
        if (hasStartedScrolling) return;
        
        hasStartedScrolling = true;
        
        // Fade out the environment container
        environmentContainer.style.transition = 'opacity 0.5s ease-out';
        environmentContainer.style.opacity = '0';
        
        setTimeout(() => {
            environmentContainer.style.display = 'none';
            container.style.display = 'flex';
            
            // Start with the intro section active
            scrollToSection(0);
        }, 500);
    }

    function scrollToSection(index) {
        if (isAnimating) return;
        
        const now = Date.now();
        if (now - lastScrollTime < 800) return; // Debounce scrolling
        lastScrollTime = now;
        
        isAnimating = true;
        currentSection = index;

        // Remove active class from all sections
        sections.forEach(section => section.classList.remove('active'));
        
        // Add active class to current section
        sections[index].classList.add('active');
        
        // Animate scroll
        container.style.transform = `translateX(-${index * 100}vw)`;

        // Reset animation flag after transition
        setTimeout(() => {
            isAnimating = false;
        }, 800);

        // If we've reached the last section, prepare to load loyalty content
        if (index === sections.length - 1) {
            // Add a delay before triggering loyalty content
            setTimeout(() => {
                const event = new CustomEvent('message', {
                    data: {
                        type: 'closePopup',
                        action: 'loadLoyalty'
                    }
                });
                window.dispatchEvent(event);
            }, 1000);
        }
    }

    function handleInitialScroll(e) {
        const scrolled = window.scrollY;
        const windowHeight = window.innerHeight;
        const environmentHeight = environmentContainer.offsetHeight;

        if (scrolled >= environmentHeight - windowHeight) {
            window.removeEventListener('scroll', handleInitialScroll);
            startScrollSections();
        }
    }

    function handleWheel(e) {
        if (!hasStartedScrolling) {
            if (e.deltaY > 0) {
                startScrollSections();
            }
            return;
        }

        e.preventDefault();
        if (isAnimating) return;

        const delta = Math.abs(e.deltaY) >= Math.abs(e.deltaX) ? e.deltaY : e.deltaX;
        
        // Use the actual delta from the event rather than a fixed threshold to better handle different scroll speeds
        if (delta > 0 && currentSection < sections.length - 1) {
            scrollToSection(currentSection + 1);
        } else if (delta < 0 && currentSection > 0) {
            scrollToSection(currentSection - 1);
        }
    }

    function handleTouchStart(e) {
        startY = e.touches[0].clientY;
        startX = e.touches[0].clientX;
    }

    function handleTouchMove(e) {
        if (!hasStartedScrolling) {
            const deltaY = e.touches[0].clientY - startY;
            if (deltaY < -50) {
                startScrollSections();
            }
            return;
        }

        if (isAnimating) return;

        const deltaY = e.touches[0].clientY - startY;
        const deltaX = e.touches[0].clientX - startX;
        const absDeltaX = Math.abs(deltaX);
        const absDeltaY = Math.abs(deltaY);

        if (absDeltaX > absDeltaY && absDeltaX > 50) {
            e.preventDefault();
            if (deltaX > 0 && currentSection > 0) {
                scrollToSection(currentSection - 1);
            } else if (deltaX < 0 && currentSection < sections.length - 1) {
                scrollToSection(currentSection + 1);
            }
        }
    }

    function handleKeydown(e) {
        if (!hasStartedScrolling) {
            if (e.key === 'ArrowDown') {
                startScrollSections();
            }
            return;
        }

        if (isAnimating) return;

        if ((e.key === 'ArrowRight' || e.key === 'ArrowDown') && currentSection < sections.length - 1) {
            scrollToSection(currentSection + 1);
        } else if ((e.key === 'ArrowLeft' || e.key === 'ArrowUp') && currentSection > 0) {
            scrollToSection(currentSection - 1);
        }
    }

    // Menu button click handler
    const menuButton = document.querySelector('.menu-button');
    if (menuButton) {
        menuButton.addEventListener('click', () => {
            console.log('Menu clicked');
        });
    }

    // Event listeners
    window.addEventListener('scroll', handleInitialScroll);
    document.addEventListener('wheel', handleWheel, { passive: false });
    document.addEventListener('touchstart', handleTouchStart, { passive: true });
    document.addEventListener('touchmove', handleTouchMove, { passive: false });
    document.addEventListener('keydown', handleKeydown);
});