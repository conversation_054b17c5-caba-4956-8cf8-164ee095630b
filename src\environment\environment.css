* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Raleway';
}

/* Initial Figma design styles */
.environment-container {
  width: 100%;
  min-width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  position: relative;
  background-color: #FFFFFF;
  z-index: 2;
  margin: 0;
}

/* Menu button styles */
.menu-button {
  position: absolute;
  top: 2rem;
  left: 2rem;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.menu-circle {
  width: 40px;
  height: 40px;
  background-color: #6B6F70;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.menu-line {
  width: 20px;
  height: 2px;
  background-color: #FFFFFF;
}

.menu-dots {
  display: flex;
  gap: 4px;
}

.menu-dot {
  width: 8px;
  height: 8px;
  background-color: #FFFFFF;
  border-radius: 50%;
}

/* Main text styles with animation properties */
.main-text {
  max-width: 1200px;
  text-align: center;
  padding: 0 1rem;
  transition: opacity 0.8s ease-out, transform 0.8s ease-out;
}

.main-text h1 {
  font-size: 50px;
  line-height: 1.4;
  font-weight: 400;
  margin-bottom: 1rem;
  color: #292B33;
}

.main-text p {
  font-size: 50px;
  line-height: 1.4;
  font-weight: 400;
  color: #292B33;
}

/* Existing scroll section styles */
.scroll-container {
  display: flex;
  width: 400vw;
  transform: translateX(0);
  transition: transform 0.8s ease-out;
  will-change: transform;
  position: relative;
  z-index: 1;
  margin: 0;
  padding: 0;
  max-width: none;
  overflow: visible;
}

.scroll-section {
  width: 100vw;
  min-width: 100vw;
  height: 100vh;
  flex-shrink: 0;
  background-image: url('../../public/rectangle.jpg');
  background-repeat: repeat;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.8s ease-out, visibility 0.8s ease-out;
  overflow: hidden;
}

.scroll-section.active {
  opacity: 1;
  visibility: visible;
}

.content-wrapper {
  max-width: 1440px;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  transform: translateY(50px);
  opacity: 0;
  transition: transform 0.8s ease-out, opacity 0.8s ease-out;
}

.scroll-section.active .content-wrapper {
  transform: translateY(0);
  opacity: 1;
}

.title {
  font-family: 'Playfair Display', serif;
  font-weight: 700;
  font-size: 35px;
  line-height: 1.33;
  text-align: center;
  color: #FFFFFF;
}

.questions-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 5px;
  text-align: center;
}

.question {
  font-family: 'Raleway';
  font-weight: 600;
  font-size: 35px;
  line-height: 1.17;
  color: #FFFFFF;
}

.description {
  font-family: 'Raleway';
  font-weight: 400;
  font-size: 26px;
  line-height: 1.23;
  margin-top: 10px;
  color: #FFFFFF;
}

/* Final scroll section styles */
#final-scroll {
  background: #FFFFFF;
  background-image: none;
}

#final-scroll .content-wrapper {
  max-width: 1440px;
  padding: 40px;
}

.final-content {
  max-width: 1200px;
  margin: 0 auto;
}

.final-text {
  font-family: 'Raleway';
  font-weight: 400;
  font-size: 35px;
  line-height: 1.174;
  color: #292B33;
  margin-bottom: 2em;
}

.final-text:last-child {
  margin-bottom: 0;
}

/* Responsive styles */
@media (max-width: 1200px) {
  .main-text h1,
  .main-text p {
    font-size: 40px;
  }
}

@media (max-width: 768px) {
  .main-text h1,
  .main-text p {
    font-size: 30px;
  }

  .menu-button {
    top: 1rem;
    left: 1rem;
  }

  .menu-circle {
    width: 32px;
    height: 32px;
  }

  .menu-line {
    width: 16px;
  }

  .menu-dot {
    width: 6px;
    height: 6px;
  }

  .title {
    font-size: 28px;
  }

  .question {
    font-size: 24px;
  }

  .description {
    font-size: 20px;
  }
}

/* Intro section styles */
#intro {
  background: #FFFFFF;
  background-image: none;
}

#intro .content-wrapper {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

#intro .main-text {
  max-width: 1200px;
  text-align: center;
}

.intro-title {
  font-family: 'Playfair Display', serif;
  font-weight: 400;
  font-size: 50px;
  line-height: 1.4;
  color: #292B33;
  margin-bottom: 1.5rem;
  text-align: center;
}

.intro-description {
  font-family: 'Playfair Display', serif;
  font-weight: 400;
  font-size: 50px;
  line-height: 1.4;
  color: #292B33;
  text-align: center;
}

/* Section specific backgrounds */
#environments {
  background-image: url('../../public/env.png');
  background-size: cover;
  background-position: center;
}

#experiences {
  background-image: url('../../public/exp.png');
  background-size: cover;
  background-position: center;
}

#emotions {
  background-image: url('../../public/emo.png');
  background-size: cover;
  background-position: center;
}

.environment-section {
  width: 100vw;
  max-width: 100%;
  overflow-x: hidden;
  position: relative;
}