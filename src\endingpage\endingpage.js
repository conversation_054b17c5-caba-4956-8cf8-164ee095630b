document.addEventListener('DOMContentLoaded', () => {
    // Text content for different states
    const textStates = [
        {
            h3: 'Leveraging data and insights to anticipate needs',
            h2: 'Deep customer understanding'
        },
        {
            h3: 'Creating relevance and emotional connections',
            h2: 'Personalised experiences'
        },
        {
            h3: 'Encouraging meaningful interactions beyond purchases',
            h2: 'A sense of community'
        }
    ];

    let currentState = -1;
    let isAnimating = false;
    const contentBelow = document.querySelectorAll('.quote');
    const deepUnderstandingSection = document.querySelector('.deep-understanding');
    const understandingText = document.querySelector('.understanding-text');
    const h3Element = document.querySelector('.understanding-text h3');
    const h2Element = document.querySelector('.understanding-text h2');

    // Initially hide only the quote sections
    contentBelow.forEach(section => {
        section.classList.add('content-locked');
    });

    function lockScroll() {
        document.body.style.overflow = 'hidden';
        document.body.classList.add('scroll-lock');
    }

    function unlockScroll() {
        document.body.style.overflow = '';
        document.body.classList.remove('scroll-lock');
    }

    function updateTextContent(direction = 'next') {
        if (isAnimating) return;
        isAnimating = true;

        const slideOutClass = direction === 'next' ? 'slide-out' : 'slide-in';
        const slideInClass = direction === 'next' ? 'slide-in' : 'slide-out';

        understandingText.classList.add(slideOutClass);
        understandingText.classList.remove('slide-active');

        setTimeout(() => {
            h3Element.textContent = textStates[currentState].h3;
            h2Element.textContent = textStates[currentState].h2;

            understandingText.classList.remove(slideOutClass);
            understandingText.classList.add(slideInClass);

            void understandingText.offsetWidth;

            understandingText.classList.remove(slideInClass);
            understandingText.classList.add('slide-active');

            setTimeout(() => {
                isAnimating = false;
                
                // If we've reached the final state, start the content sequence
                if (currentState === textStates.length - 1) {
                    setTimeout(() => {
                        unlockScroll();
                        // First show the value section
                        const valueSection = document.querySelector('.value');
                        if (valueSection) {
                            valueSection.classList.add('visible');
                            // After value section is visible, show and animate the quote
                            setTimeout(() => {
                                const quoteSection = document.querySelector('.quote');
                                const quoteText = quoteSection.querySelector('h2');
                                if (quoteSection && quoteText) {
                                    quoteSection.classList.add('visible');
                                    animateWords(quoteText, 100);
                                    // After quote animation completes, show footer
                                    onQuoteAnimationComplete(quoteText);
                                }
                            }, 1000); // Wait 1s after value section appears
                        }
                    }, 300);
                }
            }, 600);
        }, 600);
    }

    let lastWheelTime = Date.now();
    const wheelCooldown = 1000;

    window.addEventListener('wheel', (event) => {
        const now = Date.now();
        if (now - lastWheelTime < wheelCooldown) return;

        const bannerRect = deepUnderstandingSection.getBoundingClientRect();
        const isBannerInView = bannerRect.top <= window.innerHeight / 2 && bannerRect.bottom >= 0;

        if (isBannerInView) {
            if (currentState === -1) {
                currentState = 0;
                lockScroll();
                updateTextContent('next');
                lastWheelTime = now;
            } else if (!isAnimating) {
                if (event.deltaY > 0 && currentState < textStates.length - 1) {
                    // Scrolling down
                    currentState++;
                    lockScroll();
                    updateTextContent('next');
                    lastWheelTime = now;
                } else if (event.deltaY < 0 && currentState > 0) {
                    // Scrolling up
                    currentState--;
                    updateTextContent('prev');
                    lastWheelTime = now;
                }
            }
        }

        if (bannerRect.top > window.innerHeight) {
            currentState = -1;
            contentBelow.forEach(section => {
                section.classList.add('content-locked');
                section.classList.remove('content-unlocked');
            });
        }
    });

    // Function to wrap words in spans and animate them
    function animateWords(element, delay = 100) {
        if (!element) return;
        
        const text = element.textContent.trim();
        const words = text.split(' ').filter(word => word.length > 0); // Filter out empty strings
        element.innerHTML = words
            .map((word, index) => `<span style="animation-delay: ${index * delay}ms">${word}</span>`)
            .join('\u00A0'); // Use non-breaking space for consistent spacing
        
        // Add the animate class after a brief delay to ensure spans are ready
        setTimeout(() => {
            element.classList.add('animate');
        }, 100);
    }

    // Function to show the footer
    function showFooter() {
        const footer = document.querySelector('footer');
        if (footer) {
            setTimeout(() => {
                footer.classList.add('visible');
            }, 500); // Add a small delay before showing footer
        }
    }

    // Function to handle quote animation completion
    function onQuoteAnimationComplete(quoteElement) {
        const words = quoteElement.querySelectorAll('span');
        const lastWord = words[words.length - 1];
        
        // Calculate total animation time for the quote
        const lastWordDelay = parseFloat(lastWord.style.animationDelay);
        const animationDuration = 500; // fadeInWord animation duration in ms
        const totalTime = lastWordDelay + animationDuration + 500; // Add larger buffer

        // Show footer after quote animation completes
        setTimeout(showFooter, totalTime);
    }

    // Intersection Observer for scroll animations
    const observerOptions = {
        root: null,
        rootMargin: '0px',
        threshold: 0.1
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                if (entry.target.classList.contains('hero-content')) {
                    entry.target.classList.add('visible');
                }
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);

    // Only observe hero content initially
    observer.observe(document.querySelector('.hero-content'));

    // Footer text animation setup
    const footer = document.querySelector('footer');
    const footerText = footer.querySelector('h2');
    if (footerText) {
        const originalText = footerText.textContent.trim();
        footerText.textContent = `${originalText} `.repeat(8);
    }

    // Smooth scroll implementation
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
});