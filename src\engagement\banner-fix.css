/* Banner visibility fixes */
.early-access-banner {
  position: relative !important;
  overflow: visible !important;
}

.early-access-banner .banner-content {
  position: relative !important;
  overflow: visible !important;
  height: 380px !important;
  display: block !important;
}

.early-access-banner .slide {
  position: absolute !important;
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 0 !important;
  opacity: 0 !important;
  visibility: hidden !important;
  transform: none !important;
  transition: opacity 0.5s ease !important;
}

.early-access-banner .slide.active {
  opacity: 1 !important;
  visibility: visible !important;
  z-index: 5 !important;
}

.early-access-banner .slide-content {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  width: 80% !important;
  padding-right: 80px !important;
  text-align: center !important;
}

.early-access-banner h3 {
  font-family: 'Playfair Display', serif !important;
  font-size: 35px !important;
  font-style: italic !important;
  font-weight: 700 !important;
  margin-bottom: 16px !important;
  text-align: center !important;
  display: block !important;
  color: white !important;
}

.early-access-banner p {
  font-size: 26px !important;
  text-align: center !important;
  display: block !important;
  color: white !important;
}

.early-access-banner .next-button {
  position: absolute !important;
  right: 20px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  z-index: 10 !important;
  display: block !important;
  width: 60px !important;
  height: 60px !important;
  background: transparent !important;
  border: none !important;
  cursor: pointer !important;
}

.early-access-banner .next-button img {
  width: 60px !important;
  height: 60px !important;
  filter: brightness(0) invert(1) !important;
}

@media (max-width: 768px) {
  .early-access-banner .banner-content {
    height: 320px !important;
  }
  
  .early-access-banner .slide-content {
    width: 75% !important;
    padding-right: 60px !important;
  }
  
  .early-access-banner h3 {
    font-size: 28px !important;
  }
  
  .early-access-banner p {
    font-size: 22px !important;
  }
  
  .early-access-banner .next-button {
    width: 40px !important;
    height: 40px !important;
  }
  
  .early-access-banner .next-button img {
    width: 40px !important;
    height: 40px !important;
  }
}

@media (max-width: 480px) {
  .early-access-banner .banner-content {
    height: 280px !important;
  }
  
  .early-access-banner .slide-content {
    width: 70% !important;
    padding-right: 40px !important;
  }
  
  .early-access-banner h3 {
    font-size: 24px !important;
    margin-bottom: 8px !important;
  }
  
  .early-access-banner p {
    font-size: 18px !important;
  }
  
  .early-access-banner .next-button {
    right: 10px !important;
    width: 30px !important;
    height: 30px !important;
  }
  
  .early-access-banner .next-button img {
    width: 30px !important;
    height: 30px !important;
  }
} 