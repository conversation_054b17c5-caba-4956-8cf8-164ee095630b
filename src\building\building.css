* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    overflow: hidden;
    height: 100vh;
    background-color: #FFFFFF;
}

.container {
    position: relative;
    width: 100%;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem;
}

.text-wrapper {
    position: relative;
    text-align: center;
    z-index: 2;
    max-width: 800px;
    margin: 0 auto;
}

.title {
    font-family: 'Playfair Display', serif;
    font-size: 50px;
    font-weight: 400;
    line-height: 1.4;
    color: #000000;
    margin-bottom: 1rem;
}

.subtitle {
    font-family: 'Raleway';
    font-size: 35px;
    font-weight: 600;
    line-height: 1.17;
    color: #000000;
    margin-bottom: 1rem;
}

.reveal-text {
    opacity: 0;
    transform: translateY(40px);
    animation: none;
}

.reveal-text.animate {
    animation: revealText 1s cubic-bezier(0.5, 0, 0, 1) forwards;
}

.subtitle.reveal-text.animate {
    animation-delay: 0.5s;
}

.title.reveal-text.animate {
    animation-delay: 0.8s;
}

@keyframes revealText {
    from {
        opacity: 0;
        transform: translateY(40px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.image-container {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    pointer-events: none;
    z-index: 1;
}

.floating-image {
    position: absolute;
    width: 250px;
    height: 350px;
    object-fit: cover;
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.8s cubic-bezier(0.5, 0, 0, 1);
    border-radius: 8px;
}

.floating-image.visible {
    opacity: 1;
    transform: scale(1);
}

.floating-image.top {
    top: -50px;
    left: calc(50% - 400px);
    transform-origin: center bottom;
}

.floating-image.right {
    top: calc(50% - 175px);
    right: 50px;
    transform-origin: left center;
}

.floating-image.bottom {
    bottom: -50px;
    left: calc(50% + 150px);
    transform-origin: center top;
}

.floating-image.left {
    top: calc(50% - 175px);
    left: 50px;
    transform-origin: right center;
}

/* Hover effects */
.floating-image:hover {
    transform: scale(1.05);
    transition: transform 0.3s ease-out;
} 