@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,700;1,400;1,700&display=swap');

html {
  overflow-x: hidden;
  width: 100%;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Playfair Display';
  background-color: #FFFFFF;
  min-height: 100vh;
  overflow-x: hidden;
  width: 100%;
  position: relative;
}

.journey-container {
  width: 100%;
  height: 100vh;
  position: relative;
  overflow: hidden;
}

.journey-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #FFFFFF;
  transform-origin: center;
  z-index: 1;
}


        /* Building Section Styles */
        .building-section {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            background-color: #FFFFFF;
            z-index: 1000;
        }

        .building-section .container {
            position: relative;
            width: 100%;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 2rem;
        }

        .building-section .text-wrapper {
            position: relative;
            text-align: center;
            z-index: 2;
            max-width: 800px;
            margin: 0 auto;
        }

        .building-section .title {
            font-family: 'Playfair Display';
            font-size: 50px;
            font-weight: 400;
            line-height: 1.4;
            color: #000000;
            margin-bottom: 1rem;
        }

        .building-section .subtitle {
            font-size: 35px;
            font-weight: 600;
            line-height: 1.17;
            color: #000000;
            margin-bottom: 1rem;
        }

        .building-section .reveal-text {
            opacity: 0;
            transform: translateY(40px);
            animation: none;
        }

        .building-section .reveal-text.animate {
            animation: revealText 1s cubic-bezier(0.5, 0, 0, 1) forwards;
        }

        .building-section .subtitle.reveal-text.animate {
            animation-delay: 0.5s;
        }

        .building-section .title.reveal-text.animate {
            animation-delay: 0.8s;
        }

        @keyframes revealText {
            from {
                opacity: 0;
                transform: translateY(40px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .building-section .image-container {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            pointer-events: none;
            z-index: 1;
        }

        .building-section .floating-image {
            position: absolute;
            width: 250px;
            height: 350px;
            object-fit: cover;
            opacity: 0;
            transform: scale(0.8);
            transition: all 0.8s cubic-bezier(0.5, 0, 0, 1);
            border-radius: 8px;
        }

        .building-section .floating-image.visible {
            opacity: 1;
            transform: scale(1);
        }

        .building-section .floating-image.top {
            top: -50px;
            left: calc(50% - 400px);
            transform-origin: center bottom;
        }

        .building-section .floating-image.right {
            top: calc(50% - 175px);
            right: 50px;
            transform-origin: left center;
        }

        .building-section .floating-image.bottom {
            bottom: -50px;
            left: calc(50% + 150px);
            transform-origin: center top;
        }

        .building-section .floating-image.left {
            top: calc(50% - 175px);
            left: 50px;
            transform-origin: right center;
        }

        .building-section .floating-image:hover {
            transform: scale(1.05);
            transition: transform 0.3s ease-out;
        }

        /* Responsive styles for building section */
        @media (max-width: 900px) {
            .building-section .container {
                padding: 1rem;
            }

            .building-section .text-wrapper {
                max-width: 90%;
            }

            .building-section .title {
                font-size: 36px;
                margin-bottom: 0.5rem;
            }

            .building-section .subtitle {
                font-size: 24px;
            }

            .building-section .floating-image {
                width: 180px;
                height: 250px;
            }

            .building-section .floating-image.top {
                top: 20px;
                left: calc(50% - 250px);
            }

            .building-section .floating-image.right {
                top: calc(50% - 125px);
                right: 20px;
            }

            .building-section .floating-image.bottom {
                bottom: 20px;
                left: calc(50% + 70px);
            }

            .building-section .floating-image.left {
                top: calc(50% - 125px);
                left: 20px;
            }
        }

        @media (max-width: 600px) {
            .building-section .floating-image {
                width: 140px;
                height: 200px;
            }

            .building-section .floating-image.top {
                top: 10px;
                left: calc(50% - 180px);
            }

            .building-section .floating-image.right {
                top: calc(50% - 100px);
                right: 10px;
            }

            .building-section .floating-image.bottom {
                bottom: 10px;
                left: calc(50% + 40px);
            }

            .building-section .floating-image.left {
                top: calc(50% - 100px);
                left: 10px;
            }

            .building-section .title {
                font-size: 28px;
            }

            .building-section .subtitle {
                font-size: 20px;
            }
        }

        /* Connection Section Styles */
        .connection-section {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            background-color: var(--background-color);
            z-index: 1000;
            overflow-x: hidden;
            overflow-y: auto;
        }

        .connection-section .main-content {
            padding: 57px 20px 64px;
            opacity: 0;
            transform: translateY(20px);
            max-width: 100%;
            overflow-x: hidden;
        }

        .connection-section .title-section {
            text-align: center;
            margin: 101px auto 48px;
            max-width: 1290px;
            opacity: 0;
            transform: translateY(20px);
        }

        .connection-section h1 {
            font-family: 'Playfair Display';
            font-size: 50px;
            line-height: 70px;
            margin-bottom: 16px;
            opacity: 0;
        }

        .connection-section h2 {
            font-size: 35px;
            font-weight: 600;
            opacity: 0;
        }

        .connection-section .text-section {
            max-width: 1140px;
            margin: 0 auto 52px;
            color: #292B33;
            opacity: 0;
            transform: translateY(20px);
        }

        .connection-section .text-section .main-paragraph {
            font-size: 20px;
            line-height: 1.3;
            margin-bottom: 40px;
            font-weight: 400;
        }

        .connection-section .text-section .key-points {
            margin: 30px 0;
        }

        .connection-section .text-section .key-points p {
            font-size: 20px;
            line-height: 1.3;
            margin-bottom: 16px;
            font-weight: 400;
        }

        .connection-section .text-section .key-points strong {
            font-weight: 600;
            margin-right: 8px;
        }

        .connection-section .text-section p {
            font-size: 20px;
            line-height: 1.3;
            margin-bottom: 26px;
            font-weight: 400;
        }

        .connection-section .text-section strong {
            font-weight: 600;
        }

        .connection-section .blue-section {
            background-color: #476477;
            position: relative;
            overflow: hidden;
            margin: 52px 0;
            width: 100%;
            box-sizing: border-box;
            padding: 0;
            min-height: 380px;
        }

        .connection-section .blue-content {
            padding: 0;
            height: 100%;
            text-align: center;
            color: white;
            position: relative;
            max-width: 1440px;
            margin: 0 auto;
            min-height: 380px;
        }

        .connection-section .slide {
            position: absolute;
            width: 100%;
            height: 100%;
            opacity: 0;
            transform: translateX(100%);
            transition: transform 0.8s ease, opacity 0.8s ease;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 81px 20px;
        }

        .connection-section .slide.active {
            opacity: 1;
            transform: translateX(0);
        }

        .connection-section .slide.previous {
            opacity: 0;
            transform: translateX(-100%);
        }

        .connection-section .blue-content h3 {
            font-family: 'Playfair Display';
            font-size: 35px;
            font-style: italic;
            font-weight: 700;
            max-width: 622px;
            margin: 0 auto 16px;
        }

        .connection-section .blue-content p {
            font-size: 26px;
            max-width: 947px;
            margin: 16px auto 0;
        }

        .connection-section .next-button {
            position: absolute;
            right: 75px;
            top: 50%;
            transform: translateY(-50%);
            width: 60px;
            height: 60px;
            border: none;
            border-radius: 10px;
            background: transparent;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }

        .connection-section .next-button img {
            width: 60px;
            height: 60px;
            filter: brightness(0) invert(1);
        }

        @media (max-width: 1302px) {
            .connection-section .blue-section {
                min-height: 400px;
            }
            
            .connection-section .blue-content {
                min-height: 400px;
            }
            
            .connection-section .slide {
                position: absolute;
                width: 100%;
                height: 100%;
                padding: 60px 80px;
            }
            
            .connection-section .blue-content h3 {
                font-size: 32px;
                max-width: 100%;
                padding-right: 60px;
            }
            
            .connection-section .blue-content p {
                font-size: 24px;
                max-width: 100%;
                padding-right: 60px;
            }
            
            .connection-section .next-button {
                right: 20px;
                width: 50px;
                height: 50px;
            }
            
            .connection-section .next-button img {
                width: 50px;
                height: 50px;
            }
        }

        @media (max-width: 768px) {
            .connection-section .main-content {
                padding: 40px 16px;
            }

            .connection-section h1 {
                font-size: 40px;
                line-height: 56px;
            }

            .connection-section h2 {
                font-size: 28px;
            }

            .connection-section .text-section {
                font-size: 18px;
            }

            .connection-section .blue-section {
                min-height: 350px;
            }

            .connection-section .blue-content h3 {
                font-size: 28px;
                padding-right: 50px;
            }

            .connection-section .blue-content p {
                font-size: 20px;
                padding-right: 50px;
            }

            .connection-section .next-button {
                right: 15px;
                width: 40px;
                height: 40px;
            }
            
            .connection-section .next-button img {
                width: 40px;
                height: 40px;
            }
        }
        
        @media (max-width: 480px) {
            .connection-section .blue-section {
                min-height: 300px;
            }
            
            .connection-section .blue-content h3 {
                font-size: 24px;
                padding-right: 40px;
            }
            
            .connection-section .blue-content p {
                font-size: 18px;
                padding-right: 40px;
            }
            
            .connection-section .slide {
                padding: 30px 40px;
            }
            
            .connection-section .next-button {
                right: 10px;
                width: 36px;
                height: 36px;
            }
            
            .connection-section .next-button img {
                width: 36px;
                height: 36px;
            }
        }

        /* Ending Section Styles */
        .ending-section {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            background-color: #FFFFFF;
            z-index: 1000;
            overflow: auto;
        }

        .ending-section .hero {
            position: relative;
            width: 100%;
            padding: 6rem 1rem;
        }

        .ending-section .hero-content {
            max-width: 1365px;
            margin: 0 auto;
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.8s ease, transform 0.8s ease;
        }

        .ending-section .hero-text {
            text-align: center;
            margin-bottom: 4rem;
        }

        .ending-section .hero-text .subtitle {
            font-family: 'Raleway';
            font-size: 26px;
            line-height: 1.23;
            color: #000000;
            font-weight: 400;
        }

        .ending-section .hero-text h1 {
            font-family: 'Playfair Display';
            font-size: 40px;
            line-height: 1.14;
            font-weight: 400;
            font-style: normal;
            color: #000000;
        }

        .ending-section .hero-main {
            display: flex;
            flex-direction: column;
            gap: 2.5rem;
        }

        @media (min-width: 1024px) {
            .ending-section .hero-main {
                flex-direction: row;
                align-items: flex-start;
                justify-content: space-between;
            }

            .ending-section .hero-description {
                width: 65%;
            }

            .ending-section .hero-image {
                width: 35%;
            }
        }

        .ending-section .hero-description h2 {
            font-family: 'Raleway';
            font-weight: 600;
            font-size: clamp(40px, 4vw, 50px);
            line-height: 1.2;
            color: #292B33;
            letter-spacing: -0.02em;
        }

        .ending-section .hero-description h2 .word {
            display: inline-block;
            opacity: 0;
            transform: translateY(20px);
            margin-right: 0.2em;
        }

        .ending-section .hero-description h2 .word.animate {
            animation: fadeInWord 0.5s ease forwards;
        }

        /* Generate 20 animation delays for words */
        .ending-section .hero-description h2 .word:nth-child(1) { animation-delay: 0.1s; }
        .ending-section .hero-description h2 .word:nth-child(2) { animation-delay: 0.2s; }
        .ending-section .hero-description h2 .word:nth-child(3) { animation-delay: 0.3s; }
        .ending-section .hero-description h2 .word:nth-child(4) { animation-delay: 0.4s; }
        .ending-section .hero-description h2 .word:nth-child(5) { animation-delay: 0.5s; }
        .ending-section .hero-description h2 .word:nth-child(6) { animation-delay: 0.6s; }
        .ending-section .hero-description h2 .word:nth-child(7) { animation-delay: 0.7s; }
        .ending-section .hero-description h2 .word:nth-child(8) { animation-delay: 0.8s; }
        .ending-section .hero-description h2 .word:nth-child(9) { animation-delay: 0.9s; }
        .ending-section .hero-description h2 .word:nth-child(10) { animation-delay: 1s; }
        .ending-section .hero-description h2 .word:nth-child(11) { animation-delay: 1.1s; }
        .ending-section .hero-description h2 .word:nth-child(12) { animation-delay: 1.2s; }
        .ending-section .hero-description h2 .word:nth-child(13) { animation-delay: 1.3s; }
        .ending-section .hero-description h2 .word:nth-child(14) { animation-delay: 1.4s; }
        .ending-section .hero-description h2 .word:nth-child(15) { animation-delay: 1.5s; }
        .ending-section .hero-description h2 .word:nth-child(16) { animation-delay: 1.6s; }
        .ending-section .hero-description h2 .word:nth-child(17) { animation-delay: 1.7s; }
        .ending-section .hero-description h2 .word:nth-child(18) { animation-delay: 1.8s; }
        .ending-section .hero-description h2 .word:nth-child(19) { animation-delay: 1.9s; }
        .ending-section .hero-description h2 .word:nth-child(20) { animation-delay: 2s; }

        @keyframes fadeInWord {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .ending-section .hero-image img {
            width: 100%;
            height: auto;
        }

        /* Deep Understanding Section */
        .ending-section .deep-understanding {
            position: relative;
            background: #6b6f70;
            overflow: hidden;
            width: 100%;
            height: 600px;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transform: translateY(50px);
            will-change: transform, opacity;
            box-sizing: border-box;
        }

        .ending-section .texture-overlay {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            background: url(../../public/texture-1-background.png) center center / cover;
            z-index: 1;
        }

        .ending-section .deep-understanding .content {
            position: relative;
            max-width: 100%;
            height: 100%;
            padding: 4rem 1rem;
            color: white;
            opacity: 1;
            transform: none;
            display: flex;
            flex-direction: column;
            justify-content: center;
            z-index: 2;
            overflow: hidden;
        }

        .ending-section .deep-understanding .next-button {
            position: absolute;
            right: 75px;
            top: 50%;
            transform: translateY(-50%);
            width: 60px;
            height: 60px;
            border: none;
            border-radius: 10px;
            background: transparent;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }

        .ending-section .deep-understanding .next-button img {
            width: 60px;
            height: 60px;
            filter: brightness(0) invert(1);
        }

        .ending-section .deep-understanding p {
            text-align: center;
            font-family: 'Raleway';
            font-size: 26px;
            font-weight: 400;
            line-height: 1.23;
            color: #FFFFFF;
            margin-bottom: 5rem;
            max-width: 1140px;
            margin-left: auto;
            margin-right: auto;
        }

        .ending-section .understanding-text h2 {
            font-family: 'Playfair Display';
            font-size: 140px;
            line-height: 1.1;
            font-weight: 400;
            color: #FFFFFF;
        }

        /* Add responsive styles for smaller screens */
        @media (max-width: 1200px) {
            .ending-section .understanding-text h2 {
                font-size: 120px;
            }
        }

        @media (max-width: 900px) {
            .ending-section .deep-understanding {
                height: auto;
                min-height: 500px;
            }
            
            .ending-section .deep-understanding .content {
                padding: 3rem 1rem;
            }
            
            .ending-section .understanding-text h2 {
                font-size: 80px;
                line-height: 1.2;
            }
            
            .ending-section .understanding-text h3 {
                font-size: 28px;
                padding: 0 1rem;
            }
            
            .ending-section .deep-understanding p {
                font-size: 22px;
                margin-bottom: 3rem;
                padding: 0 1rem;
            }
            
            .ending-section .deep-understanding .next-button {
                right: 20px;
            }
        }

        @media (max-width: 600px) {
            .ending-section .deep-understanding {
                min-height: 400px;
            }
            
            .ending-section .understanding-text h2 {
                font-size: 42px;
                line-height: 1.2;
                padding: 0 1rem;
                word-wrap: break-word;
                hyphens: auto;
            }
            
            .ending-section .understanding-text h3 {
                font-size: 22px;
                padding: 0 1rem;
                margin-bottom: 1.5rem;
            }
            
            .ending-section .deep-understanding p {
                font-size: 18px;
                margin-bottom: 2rem;
                padding: 0 1rem;
            }

            .ending-section .deep-understanding .content {
                padding: 2rem 0.5rem;
            }
        }

        @media (max-width: 400px) {
            .ending-section .understanding-text h2 {
                font-size: 36px;
            }
            
            .ending-section .understanding-text h3 {
                font-size: 20px;
            }
            
            .ending-section .deep-understanding p {
                font-size: 16px;
            }
        }

        .ending-section .understanding-text {
            text-align: center;
            max-width: 1140px;
            margin: 0 auto;
            transition: transform 0.8s ease, opacity 0.4s ease;
            opacity: 0;
            position: relative;
            z-index: 3;
            transform: translateX(100%);
        }

        .ending-section .understanding-text h3 {
            font-family: 'Raleway';
            font-size: 35px;
            font-weight: 400;
            line-height: 1.174;
            margin-bottom: 1rem;
            color: #FFFFFF;
        }

        /* Value Section */
        .ending-section .value {
            padding: 5rem 1rem;
            overflow: visible;
        }

        .ending-section .value .card {
            max-width: 1140px;
            margin: 0 auto;
        }

        .ending-section .value .card p {
            font-size: 20px;
            line-height: 1.3;
            color: #292B33;
            opacity: 0;
            transform: translateY(20px);
            will-change: opacity, transform;
        }

        /* Reset animation state when section is hidden */
        .ending-section .value:not(.animated) .card p {
            opacity: 0;
            transform: translateY(20px);
            transition: none;
        }

        /* Quote Section */
        .ending-section .quote {
            padding: 5rem 1rem;
            text-align: center;
            background-color: #ffffff;
            position: relative;
            z-index: 10;
        }

        .ending-section .quote h2 {
            font-family: 'Playfair Display';
            font-size: 50px;
            line-height: 1.4;
            font-weight: 400;
            color: #292B33;
            max-width: 1140px;
            margin: 0 auto;
        }

        .ending-section .quote h2 .word {
            display: inline-block;
            opacity: 0;
            transform: translateY(20px);
            margin-right: 0.2em;
            will-change: opacity, transform;
        }

        /* Remove animation classes since we're handling it in JS */
        .ending-section .animation-completed h2 .word {
            opacity: 1;
            transform: translateY(0);
        }

        /* Footer Styles */
        footer {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            gap: 32px;
            padding: 45px 100px 100px;
            background-color: #476477;
            width: 100%;
            text-align: center;
        }

        .footer-logo {
            width: auto;
            height: 60px;
            display: block;
            margin: 0 auto;
        }

        .footer-title {
            font-weight: 400;
            font-size: 55px;
            line-height: 0.95em;
            color: #81C6B4;
            margin: 0;
            text-align: center;
        }

        .footer-text {
            font-weight: 400;
            font-size: 35px;
            line-height: 1.174em;
            color: #FFFFFF;
            text-align: center;
            margin: 0 auto;
            max-width: 1200px;
        }

        .footer-text b {
            font-weight: 700;
        }

        .footer-text i {
            font-style: italic;
        }

        /* Remove old footer styles */
        .text-scroll {
            display: none;
        }

        /* Animation Classes */
        .ending-section .visible {
            opacity: 1;
            transform: none;
        }

        .ending-section .scroll-lock {
            overflow: hidden;
        }

        .ending-section .scroll-lock .deep-understanding {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 1000;
        }

        .ending-section .content-locked {
            opacity: 0;
            visibility: hidden;
            transform: translateY(50px);
            transition: opacity 0.5s ease, visibility 0.5s ease, transform 0.5s ease;
        }

        .ending-section .content-unlocked {
            opacity: 1;
            visibility: visible;
            transform: none;
            transition: opacity 0.5s ease, visibility 0.5s ease, transform 0.5s ease;
        }

        .ending-section .understanding-text {
            opacity: 0;
            transform: translateX(100%);
            transition: transform 0.8s ease, opacity 0.4s ease;
        }

        .ending-section .understanding-text.slide-active {
            opacity: 1;
            transform: translateX(0);
            transition: transform 0.6s ease, opacity 0.3s ease;
        }

        .ending-section .understanding-text.slide-out {
            opacity: 0;
            transform: translateX(-100%);
            transition: transform 0.6s ease, opacity 0.3s ease;
        }

        .ending-section .understanding-text.slide-in {
            opacity: 0;
            transform: translateX(100%);
            transition: transform 0.6s ease, opacity 0.3s ease;
        }
   
.journey-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.journey-initial {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2rem;
  z-index: 3;
}

.title-left,
.title-right {
  font-size: 50px;
  font-weight: 400;
  line-height: 1.4;
  color: #000000;
  white-space: nowrap;
}

.journey-visual {
  width: 60px;
  height: 60px;
  position: relative;
  overflow: hidden;
  z-index: 2;
}

.polygon-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.polygon {
  position: absolute;
  width: 100%;
  height: 100%;
  clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
  will-change: transform;
}

.polygon-1 {
  background-color: black;
}

.polygon-2 {
  background-color: black;
  overflow: hidden;
}

.image-mask {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.journey-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  will-change: transform, opacity;
}

.journey-fullscreen {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  visibility: hidden;
  z-index: 1;
}

.fullscreen-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.fullscreen-title {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 50px;
  font-weight: 400;
  line-height: 1.4;
  text-align: center;
  color: #FFFFFF;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.5rem;
  width: 100%;
  opacity: 0;
  visibility: hidden;
}

.fullscreen-title.from-awareness {
  opacity: 0;
  visibility: hidden;
}

.fullscreen-title.to-advocacy-split {
  opacity: 0;
  visibility: hidden;
}

.fullscreen-title.to-advocacy-joined {
  opacity: 0;
  visibility: hidden;
}

.title-text {
  white-space: nowrap;
}

.title-spacer-large {
  width: 20rem;
  display: inline-block;
}

/* Animation classes */
.animate-in .polygon-1 {
  transform: translateX(0);
}

.animate-in .polygon-2 {
  transform: translateX(0);
}

.animate-in .journey-image {
  transform: scale(1);
  opacity: 1;
}

/* Responsive styles */
@media (max-width: 1200px) {
  .journey-visual {
    width: 50px;
    height: 50px;
  }
}

@media (max-width: 768px) {
  .journey-initial {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .title-left,
  .title-right,
  .fullscreen-title {
    font-size: 36px;
  }

  .journey-visual {
    width: 40px;
    height: 40px;
  }

  .title-spacer-large {
    width: 10rem;
  }
}

@media (max-width: 480px) {
  .title-left,
  .title-right,
  .fullscreen-title {
    font-size: 28px;
  }

  .journey-visual {
    width: 30px;
    height: 30px;
  }

  .title-spacer-large {
    width: 5rem;
  }
}

::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.2);
}

/* Add these responsive styles after the existing styles */

@media (max-width: 1400px) {
    .connection-section .main-content {
        padding: 40px 20px;
    }

    .connection-section .title-section {
        margin: 60px auto 40px;
        padding: 0 20px;
    }

    .connection-section .text-section {
        padding: 0 20px;
    }

    .connection-section .blue-section {
        width: 100%;
        margin-left: 0;
        padding: 0;
    }

    .connection-section .blue-content {
        padding: 0 20px;
        min-height: 400px;
    }

    .connection-section .slide {
        padding: 60px 40px;
    }

    .connection-section .blue-content h3 {
        font-size: 32px;
        max-width: 90%;
        margin: 0 auto 16px;
        padding-right: 50px;
    }

    .connection-section .blue-content p {
        font-size: 24px;
        max-width: 90%;
        margin: 16px auto 0;
        padding-right: 50px;
    }

    .connection-section .next-button {
        right: 30px;
    }
}

@media (max-width: 1200px) {
    .connection-section h1 {
        font-size: 45px;
        line-height: 1.3;
    }

    .connection-section h2 {
        font-size: 32px;
    }

    .connection-section .text-section {
        font-size: 18px;
    }

    .connection-section .blue-content h3 {
        font-size: 30px;
        padding-right: 45px;
    }

    .connection-section .blue-content p {
        font-size: 22px;
        padding-right: 45px;
    }
}

@media (max-width: 1024px) {
    .connection-section .blue-section {
        width: 100%;
        margin-left: 0;
    }

    .connection-section .blue-content {
        min-height: 380px;
    }

    .connection-section .slide {
        padding: 50px 30px;
    }

    .connection-section .blue-content h3 {
        font-size: 28px;
        padding-right: 40px;
    }

    .connection-section .blue-content p {
        font-size: 20px;
        padding-right: 40px;
    }
}

@media (max-width: 768px) {
    .connection-section .main-content {
        padding: 30px 15px;
    }

    .connection-section .title-section {
        margin: 40px auto 30px;
    }

    .connection-section h1 {
        font-size: 36px;
        line-height: 1.3;
        margin-bottom: 12px;
    }

    .connection-section h2 {
        font-size: 26px;
    }

    .connection-section .text-section {
        font-size: 16px;
        margin-bottom: 40px;
    }

    .connection-section .blue-section {
        width: 100%;
        margin-left: 0;
        min-height: 320px;
    }

    .connection-section .blue-content {
        min-height: 350px;
    }

    .connection-section .slide {
        padding: 40px 20px;
    }

    .connection-section .blue-content h3 {
        font-size: 26px;
        padding-right: 35px;
    }

    .connection-section .blue-content p {
        font-size: 18px;
        padding-right: 35px;
    }

    .connection-section .next-button {
        right: 15px;
        width: 40px;
        height: 40px;
    }

    .connection-section .next-button img {
        width: 40px;
        height: 40px;
    }
}

@media (max-width: 480px) {
    .connection-section .main-content {
        padding: 20px 10px;
    }

    .connection-section .title-section {
        margin: 30px auto 20px;
    }

    .connection-section h1 {
        font-size: 32px;
        line-height: 1.2;
    }

    .connection-section h2 {
        font-size: 22px;
    }

    .connection-section .blue-section {
        width: 100%;
        margin-left: 0;
        min-height: 300px;
    }

    .connection-section .blue-content {
        min-height: 300px;
    }

    .connection-section .slide {
        padding: 30px 15px;
    }

    .connection-section .blue-content h3 {
        font-size: 22px;
        padding-right: 30px;
    }

    .connection-section .blue-content p {
        font-size: 16px;
        padding-right: 30px;
    }

    .connection-section .next-button {
        right: 10px;
        width: 36px;
        height: 36px;
    }

    .connection-section .next-button img {
        width: 36px;
        height: 36px;
    }
}

/* Content Loading Animation */
main {
    opacity: 0;
    transform: translateY(50px);
    pointer-events: none;
}

/* Mobile Optimizations */
@media (max-width: 480px) {
    /* Optimize animations for mobile */
    .detailed-section {
        will-change: transform, opacity;
        transform: translateZ(0);
        -webkit-transform: translateZ(0);
        backface-visibility: hidden;
        -webkit-backface-visibility: hidden;
        perspective: 1000;
        -webkit-perspective: 1000;
    }

    .section-header,
    .section-content,
    .images-grid,
    .star-badge,
    .accent-box {
        will-change: transform, opacity;
        transform: translateZ(0);
        -webkit-transform: translateZ(0);
    }

    /* Reduce animation complexity on mobile */
    .building-section .floating-image {
        transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .building-section .reveal-text.animate {
        animation-duration: 0.7s;
    }

    /* Optimize touch interactions */
    .detailed-section,
    .journey-card,
    .star-badge,
    .accent-box {
        touch-action: pan-y pinch-zoom;
    }

    /* Improve scrolling performance */
    .scrolling-images-container {
        -webkit-overflow-scrolling: touch;
    }

    /* Reduce motion for users who prefer it */
    @media (prefers-reduced-motion: reduce) {
        .detailed-section,
        .section-header,
        .section-content,
        .images-grid,
        .star-badge,
        .accent-box {
            transition: none !important;
            animation: none !important;
            transform: none !important;
        }

        .building-section .floating-image {
            transition: opacity 0.3s ease !important;
        }

        .building-section .reveal-text.animate {
            animation: none !important;
            opacity: 1 !important;
            transform: none !important;
        }
    }
}

/* Popup Styles */
.popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.popup-overlay.active {
    display: flex;
}

.star-badge-popup {
    background: white;
    padding: 30px;
    border-radius: 15px;
    position: relative;
    max-width: 90%;
    width: 500px;
    display: none;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.star-badge-popup.active {
    display: block;
}

.popup-close {
    position: absolute;
    top: 15px;
    right: 15px;
    width: 24px;
    height: 24px;
    cursor: pointer;
    background: none;
    border: none;
    padding: 0;
}

.popup-number {
    font-size: 48px;
    font-weight: bold;
    margin-bottom: 20px;
}

.popup-number.blue {
    color: #476477;
}

.popup-number.purple {
    color: #8D8CC4;
}

.popup-text {
    font-size: 18px;
    line-height: 1.5;
    color: #333;
}

/* Mobile Popup Styles */
@media (max-width: 480px) {
    .star-badge-popup {
        padding: 20px;
        width: 85%;
        margin: 20px;
    }

    .popup-close {
        top: 10px;
        right: 10px;
        width: 20px;
        height: 20px;
    }

    .popup-number {
        font-size: 36px;
        margin-bottom: 15px;
    }

    .popup-text {
        font-size: 16px;
    }
}

/* Small Mobile Banner Content Fixes */
@media (max-width: 450px) {
    .banner-content {
        height: auto;
        min-height: 380px;
    }

    .banner-content .slide {
        height: auto;
        min-height: 380px;
        padding: 30px 15px;
    }

    .banner-content .slide-content {
        width: 100%;
        padding-right: 40px;
    }

    .banner-content .slide-content h3 {
        font-size: 28px;
        line-height: 1.3;
        margin-bottom: 12px;
        padding-right: 25px;
        word-wrap: break-word;
        white-space: normal;
    }

    .banner-content .slide-content p {
        font-size: 20px;
        line-height: 1.4;
        padding-right: 25px;
        word-wrap: break-word;
        white-space: normal;
    }
}

/* Extra Small Mobile Banner Content Fixes */
@media (max-width: 445px) {
    .early-access-banner .slide-content h3 {
        font-size: 20px !important;
        line-height: 1.3;
    }

    .early-access-banner .slide-content p {
        font-size: 20px ! important;
        line-height: 1.4;
    }

    

    .banner-content .slide-content p {
        font-size: 10px;
        line-height: 1.4;
    }
}

/* Contact Section */
.contact-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 24px 20px;
  width: 100%;
  max-width: 779px;
  margin: 0 auto;
}

.contact-text {
  text-align: center;
}

.contact-text p {
  font-weight: 400;
  font-size: 20px;
  line-height: 1.3;
  color: #292B33;
}

.contact-links {
  display: flex;
  align-items: center;
  gap: 16px;
}

.contact-link {
  text-decoration: none;
  transition: transform 0.3s ease;
}

.contact-link:hover {
  transform: scale(1.1);
}

.contact-icon {
  width: 32px;
  height: 32px;
}

/* References Section */
.references-section {
  width: 100%;
  max-width: 1240px;
  margin: 0 auto;
  padding: 40px 20px;
}

.references-header {
  display: flex;
  flex-direction: column;
  gap: 14px;
  margin-bottom: 40px;
}

.references-header h2 {
  font-family: 'Playfair Display';
  font-weight: 400;
  font-size: 100px;
  line-height: 0.95;
  color: #292B33;
  margin: 0;
}

.header-line {
  width: 100%;
  height: 3px;
  background-color: #2D363D;
}

.references-list {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.reference-item {
  margin-bottom: 15px;
}

.reference-item p {
  font-family: 'Playfair Display';
  font-weight: 400;
  font-size: 20px;
  line-height: 1.333;
  color: #292B33;
}

.reference-item a {
  color: #292B33;
  text-decoration: none;
  transition: color 0.3s ease;
}

.reference-item a:hover {
  color: #81C6B4;
}

/* Responsive styles */
@media (max-width: 1280px) {
  .references-section {
    padding: 40px 40px;
  }

  .references-header h2 {
    font-size: 80px;
  }
}

@media (max-width: 768px) {
  .references-section {
    padding: 30px 20px;
  }

  .references-header h2 {
    font-size: 60px;
  }

  .reference-item p {
    font-size: 18px;
  }
}

@media (max-width: 480px) {
  .references-header h2 {
    font-size: 40px;
  }

  .reference-item p {
    font-size: 16px;
  }

  .contact-text p {
    font-size: 18px;
  }

  .contact-icon {
    width: 28px;
    height: 28px;
  }
}

/* Slide-in text animation classes - expanded for all content */
.animationText,
.slide-in-element {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.8s ease, transform 0.8s ease;
}

.animationText.visible,
.slide-in-element.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Different animation delays for staggered effect */
.delay-1 { transition-delay: 0.1s; }
.delay-2 { transition-delay: 0.2s; }
.delay-3 { transition-delay: 0.3s; }
.delay-4 { transition-delay: 0.4s; }
.delay-5 { transition-delay: 0.5s; }

/* Image-specific animations */
.slide-in-image {
  opacity: 0;
  transform: translateY(20px) scale(0.97);
  transition: opacity 0.8s ease, transform 0.8s ease;
}

.slide-in-image.visible {
  opacity: 1;
  transform: translateY(0) scale(1);
}

/* Accent-box specific animations */
.accent-box {
  opacity: 0;
  transform: translateX(40px);
  transition: opacity 0.8s ease, transform 0.8s ease;
  cursor: pointer;
}

.accent-box.visible {
  opacity: 1;
  transform: translateX(0);
}

/* When accent-box is active (expanded) */
.accent-box.active {
  transform: translateX(0);
}

.accent-box .expanded-content {
  opacity: 0;
  transform: translateY(10px);
  transition: opacity 0.5s ease, transform 0.5s ease;
}

.accent-box.active .expanded-content {
  opacity: 1;
  transform: translateY(0);
}

/* Arrow animations */
.accent-box .left-arrow,
.accent-box .right-arrow {
  transition: transform 0.3s ease;
}

.accent-box:hover .left-arrow {
  transform: translateX(-3px);
}

.accent-box:hover .right-arrow {
  transform: translateX(3px);
}

.accent-box.active .left-arrow {
  transform: translateX(-5px);
}

.accent-box.active .right-arrow {
  transform: translateX(5px);
}

/* Prefers reduced motion */
@media (prefers-reduced-motion: reduce) {
  .animationText,
  .slide-in-element,
  .slide-in-image,
  .accent-box {
    transition: opacity 0.3s ease;
    transform: none;
  }
}

/* Add to existing CSS */
.scrolling-images-container {
  overflow: hidden !important; /* Force hidden overflow to remove scrollbars */
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.scrolling-images-container::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

/* Footer responsive styles */
@media (max-width: 1024px) {
    footer {
        padding: 40px 60px 80px;
    }
    
    .footer-title {
        font-size: 45px;
    }
    
    .footer-text {
        font-size: 28px;
    }
}

@media (max-width: 768px) {
    footer {
        padding: 30px 40px 60px;
    }
    
    .footer-title {
        font-size: 36px;
    }
    
    .footer-text {
        font-size: 24px;
    }
}

@media (max-width: 480px) {
    footer {
        padding: 25px 20px 40px;
    }
    
    .footer-logo {
        height: 45px;
    }
    
    .footer-title {
        font-size: 28px;
    }
    
    .footer-text {
        font-size: 18px;
        line-height: 1.4em;
    }
}

