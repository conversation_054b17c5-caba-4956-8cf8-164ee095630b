const handleEnvironmentWheel = (e) => {
    if (!isInEnvironmentSection()) {
        // If we're not in the environment section, remove this handler
        window.removeEventListener('wheel', handleEnvironmentWheel, { passive: false });
        // Restore normal scrolling
        document.body.style.overflow = 'auto';
        document.documentElement.style.overflow = 'auto';
        return;
    }

    e.preventDefault();
    // ... rest of the existing wheel handling code ...
};

// Add cleanup when leaving the environment section
const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (!entry.isIntersecting) {
            window.removeEventListener('wheel', handleEnvironmentWheel, { passive: false });
            document.body.style.overflow = 'auto';
            document.documentElement.style.overflow = 'auto';
        }
    });
}, { threshold: 0.5 });

const environmentSection = document.querySelector('.environment-section');
if (environmentSection) {
    observer.observe(environmentSection);
} 