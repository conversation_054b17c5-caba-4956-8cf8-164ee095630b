/* Base styles */
body {
 margin: 0;
 font-family: "Raleway", sans-serif;
 color: #292b33;
 height: 100%;
 overflow-y: auto;
 background: transparent;
}

.form-header {
 font-family: "Raleway", sans-serif;
 font-weight: 400;
 color: #292b33;
 text-align: center;
 margin: 0;

 p {
  font-size: 20px;
  line-height: 26px;
 }
}
/* Popup overlay */
.popup-overlay {
 position: fixed;
 top: 0;
 left: 0;
 width: 100%;
 height: 100%;
 background-color: rgba(0, 0, 0, 0.5);
 display: flex;
 justify-content: center;
 align-items: center;
 z-index: 1000;
 padding: 20px;
}

/* Popup iframe */
.popup-overlay iframe {
 width: 100%;
 max-width: 1051px;
 height: 90vh;
 border: none;
 border-radius: 10px;
}

/* Form container */
.form-container {
 position: relative;
 width: 100%;
 max-width: 1051px;
 background-color: white;
 padding: 2rem;
 margin: 0 auto;
 min-height: 100%;
 box-sizing: border-box;
 display: flex;
 flex-direction: column;
 max-height: 90vh;
 overflow-y: auto;
 border-radius: 10px;
}

.close-button {
 position: absolute;
 top: 29px;
 left: 2rem;
 background: none;
 border: none;
 cursor: pointer;
 padding: 0;
}

.icon {
 width: 22px;
 height: 22px;
}

.form-content {
 width: 100%;
 display: flex;
 flex-direction: column;
 align-items: center;
 gap: 2.5rem;
 margin-top: 4.125rem;
}

/* Header styles */
.form-header {
 display: flex;
 flex-direction: column;
 align-items: center;
 gap: 0.625rem;
 width: 100%;
}

.form-header h1 {
 font-family: "Playfair Display", serif;
 font-weight: bold;
 font-style: italic;
 color: #292b33;
 font-size: 2.8125rem;
 text-align: center;
 margin: 0;
}

.form-header p {
 font-size: 1rem;
 font-family: "Raleway", sans-serif;
 text-align: center;
 margin: 0;
}

/* Form styles */
.form {
 width: 100%;
 max-width: 951px;
 display: flex;
 flex-direction: column;
 gap: 1.875rem;
}

.form-fields {
 display: flex;
 flex-direction: column;
 gap: 1.875rem;
 width: 100%;
}

.form-row {
 display: flex;
 flex-wrap: wrap;
 gap: 1.25rem;
}

.form-group {
 flex: 1;
 min-width: 300px;
}

.full-width {
 width: 100%;
}

label {
 display: block;
 font-family: "Playfair Display", serif;
 font-weight: normal;
 color: #292b33;
 font-size: 1.875rem;
 margin-bottom: 0.5rem;
}

input[type="text"],
input[type="email"] {
 width: 100%;
 border: none;
 border-bottom: 1px solid #292b33;
 border-radius: 0;
 padding: 0.5rem 0;
 background-color: transparent;
 font-size: 1rem;
}

input[type="text"]:focus,
input[type="email"]:focus {
 outline: none;
 box-shadow: none;
}

.error-message {
 font-family: "Raleway", sans-serif;
 font-weight: 600;
 color: #bd6c6c;
 font-size: 1rem;
 margin-top: 0.25rem;
 display: none;
}

/* Checkbox styles */
.checkbox-group {
 display: flex;
 flex-direction: column;
 gap: 1rem;
}

.checkbox-item {
 display: flex;
 align-items: center;
 gap: 0.5rem;
}

input[type="checkbox"] {
 margin-top: 0.25rem;
 width: 20px;
 height: 20px;
 border-radius: 0;
 border: 1px solid #292b33;
 appearance: none;
 -webkit-appearance: none;
 cursor: pointer;
 position: relative;
}

input[type="checkbox"]:checked {
 background-color: #292b33;
}

input[type="checkbox"]:checked::after {
 content: "✓";
 color: white;
 position: absolute;
 left: 50%;
 top: 50%;
 transform: translate(-50%, -50%);
}

.checkbox-item label {
 font-family: "Raleway", sans-serif;
 font-weight: normal;
 font-size: 0.875rem;
 margin-bottom: 0;
 cursor: pointer;
}

.underline {
 text-decoration: underline;
}

/* Legal text */
.legal-text {
 font-family: "Raleway", sans-serif;
 font-weight: normal;
 color: #292b33;
 font-size: 0.6875rem;
 margin: 0;
}

/* Submit button */
.submit-button-container {
 display: flex;
 justify-content: center;
 margin-top: 1rem;
}

.submit-button {
 width: 179px;
 height: 58px;
 background-color: #81c6b4;
 border: none;
 border-radius: 0;
 font-family: "Raleway", sans-serif;
 font-weight: 600;
 color: white;
 font-size: 1.375rem;
 cursor: pointer;
}

.submit-button:hover {
 background-color: #6db3a1;
}

/* Error states */
.error-input {
 border-bottom-color: #bd6c6c !important;
 color: #bd6c6c;
}

.error-label {
 color: #bd6c6c;
}

.error-checkbox {
 border-color: #bd6c6c !important;
}

/* Responsive styles */
@media (max-height: 800px) {
 .form-content {
  margin-top: 2rem;
  gap: 1.5rem;
 }

 .form-header h1 {
  font-size: 2.25rem;
 }

 .form-fields {
  gap: 1rem;
 }

 label {
  font-size: 1.5rem;
  margin-bottom: 0.25rem;
 }

 .checkbox-group {
  gap: 0.5rem;
 }

 .legal-text {
  font-size: 0.625rem;
 }
}

@media (max-height: 600px) {
 input[type="checkbox"] {
  width: 14px;
  height: 14px;
  margin: 0;
 }

 .form-content {
  margin-top: 1.5rem;
  gap: 1rem;
 }

 .form-header h1 {
  font-size: 2rem;
 }

 .form-header p {
  font-size: 0.875rem;
 }

 label {
  font-size: 1.25rem;
 }

 .checkbox-item label {
  font-size: 0.75rem;
 }

 .submit-button {
  height: 48px;
  font-size: 1.125rem;
 }
}

@media (max-width: 480px) {
 .form-container {
  padding: 1rem;
 }

 .close-button {
  top: 1rem;
  left: 1rem;
 }

 .form-group {
  min-width: 100%;
 }

 .form-header h1 {
  font-size: 1.75rem;
 }
}

/* Blurred section styles */
#blur-style,
.blurred-section {
 position: relative !important;
 width: 100% !important;
 user-select: none !important;
 -webkit-user-select: none !important;
 -moz-user-select: none !important;
 -ms-user-select: none !important;
}

.blurred-section::before {
 content: "";
 position: absolute;
 top: 0;
 left: 0;
 right: 0;
 bottom: 0;
 width: 100vw !important;
 left: 50% !important;
 transform: translateX(-50%) !important;
 background: rgba(255, 255, 255, 0.3);
 backdrop-filter: blur(5px);
 -webkit-backdrop-filter: blur(5px);
 z-index: 100;
 pointer-events: auto !important; /* Capture all interactions */
 cursor: not-allowed;
}

/* Make sure the container inside blurred section doesn't affect blur coverage */
.blurred-section .container {
 position: relative;
 z-index: 1;
}

/* Disable all interactive elements within blurred sections */
.blurred-section a,
.blurred-section button,
.blurred-section input,
.blurred-section textarea,
.blurred-section select,
.blurred-section [role="button"],
.blurred-section [tabindex] {
 pointer-events: none !important;
 user-select: none !important;
 -webkit-user-select: none !important;
 -moz-user-select: none !important;
 -ms-user-select: none !important;
}

/* Fix for horizontal scroll sections */
.horizontal-scroll,
.horizontal-scroll2,
.horizontal-scroll3 {
 z-index: 1; /* Ensure scroll container is behind the blur overlay */
}

.pin {
 z-index: 1; /* Ensure pinned elements are behind blur overlay */
}

#access-message {
 position: fixed;
 left: 0;
 right: 0;
 bottom: 0;
 background-color: rgba(0, 0, 0, 0.7);
 color: white;
 padding: 20px;
 z-index: 999;
 text-align: center;
}

.message-content {
 max-width: 600px;
 margin: 0 auto;
}

#access-message h3 {
 margin: 0 0 15px 0;
 font-size: 18px;
}

#unlock-button {
 background-color: #81c6b4;
 color: white;
 border: none;
 padding: 10px 20px;
 cursor: pointer;
 font-weight: bold;
 z-index: 1000;
 position: relative;
}
