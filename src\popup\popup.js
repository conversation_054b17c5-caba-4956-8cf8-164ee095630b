const GOOGLE_SCRIPT_URL =
 "https://script.google.com/macros/s/AKfycbyGO5v2J_x5iuYM0eq2Eff5VAQOVl4pCyybk94gmlwM8AuRfEXszyAj4Maxu8nrswl4/exec";

document.addEventListener("DOMContentLoaded", function () {
 const form = document.getElementById("custom-form");
 const closeButton = document.querySelector(".close-button");

 const errorMessages = {
  name: document.getElementById("nameError"),
  surname: document.getElementById("surnameError"),
  email: document.getElementById("emailError"),
  privacyPolicy: document.getElementById("privacyError"),
 };

 if (closeButton) {
  closeButton.addEventListener("click", function () {
   window.parent.postMessage({ type: "closePopup" }, "*");
  });
 }

 function validateEmail(email) {
  const re =
   /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  return re.test(String(email).toLowerCase());
 }

 function isPersonalEmail(email) {
  const personalDomains = [
   "gmail.com",
   "hotmail.com",
   "outlook.com",
   "yahoo.com",
   "aol.com",
   "icloud.com",
   "mail.com",
   "protonmail.com",
   "zoho.com",
   "yandex.com",
   "gmx.com",
   "live.com",
   "msn.com",
  ];

  const domain = email.split("@")[1]?.toLowerCase();
  return personalDomains.includes(domain);
 }

 function validateField(field) {
  const input = document.getElementById(field);
  const errorMsg = errorMessages[field];

  if (!input.value.trim()) {
   errorMsg.textContent = `${
    field.charAt(0).toUpperCase() + field.slice(1)
   } is required`;
   errorMsg.style.display = "block";
   input.classList.add("error-input");
   return false;
  } else if (field === "email") {
   if (!validateEmail(input.value.trim())) {
    errorMsg.textContent = "Please enter a valid email address";
    errorMsg.style.display = "block";
    input.classList.add("error-input");
    return false;
   } else if (isPersonalEmail(input.value.trim())) {
    errorMsg.textContent =
     "Oops! Please use your work email (e.g., <EMAIL>) so we can better assist you.";
    errorMsg.style.display = "block";
    input.classList.add("error-input");
    return false;
   }
  }

  errorMsg.style.display = "none";
  input.classList.remove("error-input");
  return true;
 }

 function validatePrivacyPolicy() {
  const privacyCheckbox = document.getElementById("privacy_policy");
  const errorMsg = errorMessages.privacyPolicy;
  const label = document.getElementById("privacyLabel");

  if (!privacyCheckbox.checked) {
   errorMsg.textContent = "You must accept the Privacy Policy";
   errorMsg.style.display = "block";
   privacyCheckbox.classList.add("error-checkbox");
   label.classList.add("error-label");
   return false;
  } else {
   errorMsg.style.display = "none";
   privacyCheckbox.classList.remove("error-checkbox");
   label.classList.remove("error-label");
   return true;
  }
 }

 form.addEventListener("submit", function (e) {
  e.preventDefault();

  const isNameValid = validateField("name");
  const isSurnameValid = validateField("surname");
  const isEmailValid = validateField("email");
  const isPrivacyValid = validatePrivacyPolicy();

  if (isNameValid && isSurnameValid && isEmailValid && isPrivacyValid) {
   // Show loading state
   const submitButton = form.querySelector('button[type="submit"]');
   const originalButtonText = submitButton.textContent;
   submitButton.disabled = true;
   submitButton.textContent = "Sending...";

   const data = {
    name: document.getElementById("name").value,
    surname: document.getElementById("surname").value,
    email: document.getElementById("email").value,
    company: document.getElementById("company").value,
    role: document.getElementById("role").value,
    privacy_policy: document.getElementById("privacy_policy").checked
     ? "Yes"
     : "No",
    receive_more_info: document.getElementById("receive_more_info").checked
     ? "Yes"
     : "No",
    origin: document.getElementById("origin").value,
   };

   try {
    fetch(GOOGLE_SCRIPT_URL, {
     method: "POST",
     body: JSON.stringify(data),
     headers: {
      "Content-Type": "application/json",
     },
     mode: "no-cors",
    })
     .then((response) => {
      // Close popup immediately after successful submission
      window.parent.postMessage(
       {
        type: "closePopup",
        action: "loadLoyalty",
        formData: data,
        formSubmitted: true,
        showStickyMenu: true,
       },
       "*"
      );
      form.reset();
     })
     .catch((error) => {
      // Reset button state
      submitButton.disabled = false;
      submitButton.textContent = originalButtonText;
      alert("There was an error submitting the form. Please try again later.");
     });
   } catch (error) {
    // Reset button state
    submitButton.disabled = false;
    submitButton.textContent = originalButtonText;
    alert(
     "There was an error processing your submission. Please try again later."
    );
   }
  }
 });

 document
  .getElementById("name")
  .addEventListener("blur", () => validateField("name"));
 document
  .getElementById("surname")
  .addEventListener("blur", () => validateField("surname"));
 document
  .getElementById("email")
  .addEventListener("blur", () => validateField("email"));
 document
  .getElementById("privacy_policy")
  .addEventListener("change", validatePrivacyPolicy);

 // Clear error styling when user is typing
 document.getElementById("name").addEventListener("input", function () {
  this.classList.remove("error-input");
  errorMessages.name.style.display = "none";
 });

 document.getElementById("surname").addEventListener("input", function () {
  this.classList.remove("error-input");
  errorMessages.surname.style.display = "none";
 });

 document.getElementById("email").addEventListener("input", function () {
  this.classList.remove("error-input");
  errorMessages.email.style.display = "none";
 });
});
