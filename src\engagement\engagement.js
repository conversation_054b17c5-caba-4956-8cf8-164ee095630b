gsap.registerPlugin(ScrollTrigger);

document.addEventListener("DOMContentLoaded", () => {
 // Check if we have a hash fragment indicating direct navigation to a section
 if (window.location.hash === "#end") {
  console.log("Direct navigation to end section detected via hash fragment");

  // Hide the journey container and animations
  const journeyContainer = document.querySelector(".journey-container");
  if (journeyContainer) {
   journeyContainer.style.display = "none";
  }

  // Set the customer content visible
  const customerContent = document.querySelector("main");
  if (customerContent) {
   customerContent.style.opacity = 1;
   customerContent.style.transform = "translateY(0)";
   customerContent.style.pointerEvents = "auto";
  }

  // Kill all ScrollTrigger instances
  if (window.ScrollTrigger && window.ScrollTrigger.getAll) {
   window.ScrollTrigger.getAll().forEach((trigger) => trigger.kill());
  }

  // Kill all GSAP animations
  if (window.gsap && window.gsap.killTweensOf) {
   window.gsap.killTweensOf("*");
  }

  // Enable scrolling
  document.body.style.overflow = "auto";

  // Immediately show ending section
  setTimeout(() => {
   const endingSection = document.querySelector(".ending-section");
   if (endingSection) {
    // Hide all other sections
    Array.from(
     document.querySelectorAll("main > *:not(.ending-section)")
    ).forEach((section) => {
     section.style.display = "none";
    });

    // Make sure the ending section is visible
    endingSection.style.display = "block";
    endingSection.style.visibility = "visible";
    endingSection.style.opacity = "1";

    // Show all components
    Array.from(
     endingSection.querySelectorAll("section, .quote, .value, footer")
    ).forEach((el) => {
     el.style.display = "block";
     el.style.visibility = "visible";
     el.style.opacity = "1";
    });

    // Make sure we're at the top
    window.scrollTo(0, 0);
   }
  }, 100);
 }

 // Remove initial content loading animation that uses GSAP
 /* 
    // Initial content loading animation
    const mainContent = document.querySelector('main');
    if (mainContent) {
        // Set initial state
        gsap.set(mainContent, {
            opacity: 0,
            y: 50,
            pointerEvents: 'none'
        });
        
        // Animate in with GSAP
        gsap.to(mainContent, {
            opacity: 1,
            y: 0,
            duration: 0.5,
            ease: "power2.out",
            pointerEvents: 'auto'
        });
    }
    */

 const container = document.querySelector(".journey-container");
 const background = document.querySelector(".journey-background");
 const journeyContent = document.querySelector(".journey-content");
 const titleLeft = document.querySelector(".title-left");
 const titleRight = document.querySelector(".title-right");
 const polygon1 = document.querySelector(".polygon-1");
 const polygon2 = document.querySelector(".polygon-2");
 const journeyImage = document.querySelector(".journey-image");
 const fullscreenSection = document.querySelector(".journey-fullscreen");
 const fromAwareness = document.querySelector(".from-awareness");
 const toAdvocacySplit = document.querySelector(".to-advocacy-split");
 const toAdvocacyJoined = document.querySelector(".to-advocacy-joined");
 const customerContent = document.querySelector("main");
 const finalCard = document.querySelector(".final-card");
 const buildingSection = document.querySelector(".building-section");
 const floatingImages = document.querySelectorAll(".floating-image");
 const revealTexts = document.querySelectorAll(
  ".building-section .reveal-text"
 );
 const connectionSection = document.querySelector(".connection-section");
 const connectionContent = document.querySelector(
  ".connection-section .main-content"
 );
 const connectionTitle = document.querySelector(
  ".connection-section .title-section"
 );
 const connectionTexts = document.querySelectorAll(
  ".connection-section .text-section"
 );
 const connectionBlueSection = document.querySelector(
  ".connection-section .blue-section"
 );
 const connectionSlides = document.querySelectorAll(
  ".connection-section .slide"
 );
 const nextButton = document.querySelector(".connection-section .next-button");

 let isCustomerContentVisible = false;
 let isBuildingContentVisible = false;
 let isConnectionContentVisible = false;
 let isAnimating = false;
 let currentSlide = 1;
 const totalSlides = connectionSlides.length;
 let isEndingContentVisible = false;
 let isAtTop = true;
 let wheelThrottleTimeout = null;

 // Add wheel event listener to detect navigation back to environment content
 window.addEventListener("wheel", handleInitialWheel, { passive: false });
 window.addEventListener("scroll", handleScroll);

 // Handle initial scroll position
 function handleScroll() {
  isAtTop = window.scrollY < 5; // Much stricter check for top of page (less than 5px)
 }

 // Handle wheel events at the top of the engagement content
 function handleInitialWheel(e) {
  // Only handle wheel up at the top of the page (scrollY exactly 0) to avoid
  // incorrect navigation from middle of the page
  const endingSection = document.querySelector(".ending-section");
  const connectionSection = document.querySelector(".connection-section");

  const isInEndingSection =
   endingSection && window.getComputedStyle(endingSection).display !== "none";
  const isInConnectionSection =
   connectionSection &&
   window.getComputedStyle(connectionSection).display !== "none";
  const isExactlyAtTop = window.scrollY === 0;

  // Never navigate from ending section to connection section with wheel

  // Special handling for navigation from connection section:
  // If in connection section and scrolling up, always go to building section instead of environment
  if (
   isInConnectionSection &&
   (e.deltaY < -20 ||
    (Math.abs(e.deltaX) > Math.abs(e.deltaY) && e.deltaX < -20))
  ) {
   // When at the top of the connection section
   if (isExactlyAtTop && !isAnimating) {
    // Prevent multiple triggers
    if (wheelThrottleTimeout) return;

    e.preventDefault();

    wheelThrottleTimeout = setTimeout(() => {
     wheelThrottleTimeout = null;
    }, 1000);

    // Show building section instead of navigating to environment
    // Show building section first, then hide connection section
    buildingSection.style.display = "block";
    gsap.to(buildingSection, {
     opacity: 1,
     duration: 0.3,
     onStart: () => {
      isBuildingContentVisible = true;
      isAnimating = true;
      // Start building section animations
      revealTexts.forEach((text) => {
       text.classList.add("animate");
      });
      floatingImages.forEach((img, index) => {
       setTimeout(() => {
        img.classList.add("visible");
       }, index * 200);
      });
     },
    });

    // Hide connection section with slight delay
    gsap.to(connectionSection, {
     opacity: 0,
     duration: 0.3,
     delay: 0.1,
     onComplete: () => {
      connectionSection.style.display = "none";
      isConnectionContentVisible = false;
      isAnimating = false;
     },
    });

    return;
   }
   // If not at the top, let normal scrolling happen
   return;
  }

  // Only go to environment when in main journey view AND exactly at top
  if (
   !isInEndingSection &&
   !isInConnectionSection &&
   isExactlyAtTop &&
   (e.deltaY < -20 ||
    (Math.abs(e.deltaX) > Math.abs(e.deltaY) && e.deltaX < -20))
  ) {
   // Prevent multiple triggers
   if (wheelThrottleTimeout) return;

   e.preventDefault();

   wheelThrottleTimeout = setTimeout(() => {
    wheelThrottleTimeout = null;
   }, 1000);

   navigateToEnvironment();
  }
 }

 // Function to navigate back to environment content
 function navigateToEnvironment() {
  console.log("Navigating back to environment content");

  // Clean up event listeners
  window.removeEventListener("wheel", handleInitialWheel);
  window.removeEventListener("scroll", handleScroll);

  // Notify parent window to show environment content
  if (window.parent) {
   window.parent.postMessage(
    {
     type: "navigateToEnvironment",
     action: "showEnvironmentContent",
    },
    "*"
   );
  }
 }

 // Initial states
 gsap.set([polygon1, polygon2], {
  clipPath: "polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%)",
 });
 gsap.set(journeyImage, { scale: 1.2, opacity: 0 });
 gsap.set(fullscreenSection, { opacity: 0, visibility: "hidden" });
 gsap.set([fromAwareness, toAdvocacySplit, toAdvocacyJoined], {
  opacity: 0,
  visibility: "hidden",
  y: 20,
 });
 gsap.set(buildingSection, {
  display: "none",
  opacity: 0,
 });
 gsap.set(connectionSection, {
  display: "none",
  opacity: 0,
 });
 gsap.set(".connection-section .title-section", {
  opacity: 0,
  y: 20,
 });
 gsap.set(
  ".connection-section .title-section h1, .connection-section .title-section h2",
  {
   opacity: 0,
   y: 20,
  }
 );

 // Create the main timeline
 const tl = gsap.timeline({
  scrollTrigger: {
   trigger: container,
   start: "top top",
   end: "+=600%",
   pin: true,
   scrub: 1,
   anticipatePin: 1,
   onUpdate: (self) => {
    // Only show customer content after the entire timeline is complete
    if (self.progress >= 1 && !isCustomerContentVisible) {
     showCustomerContent();
    }
   },
  },
 });

 // Make showConnectionSection accessible globally
 window.showConnectionSection = showConnectionSection;

 // Animation sequence
 tl
  // First state to second state
  .to(".journey-visual", {
   width: "100vw",
   height: "100vh",
   duration: 1,
   ease: "power2.inOut",
  })
  .to(
   [polygon1, polygon2],
   {
    clipPath: "polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)",
    duration: 1,
    ease: "power2.inOut",
   },
   "<"
  )
  .to(
   journeyImage,
   {
    scale: 1,
    opacity: 1,
    duration: 0.5,
   },
   "<"
  )
  .to(
   [titleLeft, titleRight],
   {
    opacity: 0,
    y: -20,
    duration: 0.3,
   },
   ">-0.2"
  )

  // Second state - show "from Awareness"
  .to(fullscreenSection, {
   opacity: 1,
   visibility: "visible",
   duration: 0.5,
  })
  .to(
   [polygon1, polygon2],
   {
    opacity: 0,
    duration: 0.3,
   },
   "<"
  )
  .to(
   fromAwareness,
   {
    opacity: 1,
    visibility: "visible",
    y: 0,
    duration: 0.5,
   },
   "<0.2"
  )
  .to(
   background,
   {
    backgroundColor: "#D8BB93",
    duration: 0.5,
   },
   "<"
  )

  // Third state - transition to "to [space] Advocacy"
  .to(
   fromAwareness,
   {
    opacity: 0,
    y: -20,
    duration: 0.3,
   },
   "+=1.5"
  )
  .to(
   toAdvocacySplit,
   {
    opacity: 1,
    visibility: "visible",
    y: 0,
    duration: 0.8,
   },
   ">"
  )

  // Final state - transition to "to Advocacy"
  .to(
   toAdvocacySplit,
   {
    opacity: 0,
    y: -20,
    duration: 0.3,
   },
   "+=1.5"
  )
  .to(
   toAdvocacyJoined,
   {
    opacity: 1,
    visibility: "visible",
    y: 0,
    duration: 0.8,
   },
   ">"
  )
  // Add a longer delay at the end to ensure the final animation is visible
  .to({}, { duration: 1.5 });

 function showCustomerContent() {
  if (!isCustomerContentVisible) {
   isCustomerContentVisible = true;

   setTimeout(() => {
    // Remove GSAP animation and use direct style setting
    const customerContent = document.querySelector("main");
    if (customerContent) {
     customerContent.style.opacity = 1;
     customerContent.style.transform = "translateY(0)";
     customerContent.style.pointerEvents = "auto";
    }

    document.body.style.overflow = "auto";
   }, 300);
  }
 }

 function showBuildingSection() {
  if (!isBuildingContentVisible && !isAnimating) {
   isAnimating = true;
   isBuildingContentVisible = true;

   // Reset text animations first
   revealTexts.forEach((text) => {
    text.classList.remove("animate");
    // Force reflow
    void text.offsetWidth;
   });

   // Show building section
   buildingSection.style.display = "block";
   gsap.to(buildingSection, {
    opacity: 1,
    duration: 0.5,
    onComplete: () => {
     // Animate building section elements
     revealTexts.forEach((text) => {
      text.classList.add("animate");
     });

     floatingImages.forEach((img, index) => {
      // Reset image state first
      img.classList.remove("visible");
      // Force reflow
      void img.offsetWidth;
      // Add visible class with delay
      setTimeout(() => {
       img.classList.add("visible");
      }, index * 200);
     });

     setTimeout(() => {
      isAnimating = false;
     }, 1000); // Increased time to ensure animations complete
    },
   });
  }
 }

 function hideBuildingSection() {
  if (isBuildingContentVisible && !isAnimating) {
   isAnimating = true;

   // Hide floating images
   floatingImages.forEach((img) => {
    img.classList.remove("visible");
   });

   // Remove text animations
   revealTexts.forEach((text) => {
    text.classList.remove("animate");
   });

   // Fade out building section
   gsap.to(buildingSection, {
    opacity: 0,
    duration: 0.3,
    onComplete: () => {
     buildingSection.style.display = "none";
     isBuildingContentVisible = false;
    },
   });

   setTimeout(() => {
    isAnimating = false;
   }, 500);
  }
 }

 function showConnectionSection() {
  if (!isConnectionContentVisible && !isAnimating) {
   isAnimating = true;
   isConnectionContentVisible = true;

   // Reset quote and value section animation states
   const quoteSection = document.querySelector(".ending-section .quote");
   const valueSection = document.querySelector(".ending-section .value");

   if (quoteSection) {
    quoteSection.classList.remove("animation-completed");
    const words = quoteSection.querySelectorAll(".word");
    words.forEach((word) => {
     word.style.opacity = "0";
     word.style.transform = "translateY(20px)";
     word.style.transition = "none";
    });
   }

   if (valueSection) {
    valueSection.classList.remove("animated");
    const paragraphs = valueSection.querySelectorAll("p");
    paragraphs.forEach((p) => {
     p.style.opacity = "0";
     p.style.transform = "translateY(20px)";
     p.style.transition = "none";
    });
   }

   // Hide any other sections that might be visible
   if (isEndingContentVisible) {
    if (endingSection && endingSection.section) {
     endingSection.section.style.display = "none";
    }
    isEndingContentVisible = false;
   }

   // First hide building section if it's visible
   if (isBuildingContentVisible) {
    floatingImages.forEach((img) => {
     img.classList.remove("visible");
    });

    revealTexts.forEach((text) => {
     text.classList.remove("animate");
    });

    // Fade out building section
    gsap.to(buildingSection, {
     opacity: 0,
     duration: 0.3,
     onComplete: () => {
      buildingSection.style.display = "none";
      isBuildingContentVisible = false;
      showConnectionContent();
     },
    });
   } else {
    showConnectionContent();
   }
  }
 }

 function showConnectionContent() {
  // Show connection section
  connectionSection.style.display = "block";
  gsap.to(connectionSection, {
   opacity: 1,
   duration: 0.5,
  });

  // Animate connection section elements
  gsap.to(connectionContent, {
   opacity: 1,
   y: 0,
   duration: 0.5,
   delay: 0.2,
  });

  // Animate title section and its elements separately
  gsap.to(connectionTitle, {
   opacity: 1,
   y: 0,
   duration: 0.5,
   delay: 0.4,
  });

  // Animate h1 and h2 within title section
  const titleH1 = connectionTitle.querySelector("h1");
  const titleH2 = connectionTitle.querySelector("h2");
  if (titleH1) {
   gsap.to(titleH1, {
    opacity: 1,
    y: 0,
    duration: 0.5,
    delay: 0.5,
   });
  }
  if (titleH2) {
   gsap.to(titleH2, {
    opacity: 1,
    y: 0,
    duration: 0.5,
    delay: 0.6,
   });
  }

  connectionTexts.forEach((text, index) => {
   gsap.to(text, {
    opacity: 1,
    y: 0,
    duration: 0.5,
    delay: 0.7 + index * 0.2,
   });
  });

  gsap.to(connectionBlueSection, {
   opacity: 1,
   duration: 0.5,
   delay: 0.8,
   onComplete: () => {
    setTimeout(() => {
     isAnimating = false;
    }, 500);
   },
  });
 }

 function hideConnectionSection() {
  if (isConnectionContentVisible && !isAnimating) {
   isAnimating = true;

   // Reset all animations
   gsap.to(
    [
     connectionContent,
     connectionTitle,
     ...connectionTexts,
     connectionBlueSection,
    ],
    {
     opacity: 0,
     y: 20,
     duration: 0.3,
    }
   );

   // Fade out connection section
   gsap.to(connectionSection, {
    opacity: 0,
    duration: 0.3,
    onComplete: () => {
     connectionSection.style.display = "none";
     isConnectionContentVisible = false;
     isAnimating = false;

     // Reset quote and value section animation states
     const quoteSection = document.querySelector(".ending-section .quote");
     const valueSection = document.querySelector(".ending-section .value");

     if (quoteSection) {
      quoteSection.classList.remove("animation-completed");
      const words = quoteSection.querySelectorAll(".word");
      words.forEach((word) => {
       word.style.opacity = "0";
       word.style.transform = "translateY(20px)";
       word.style.transition = "none";
      });
     }

     if (valueSection) {
      valueSection.classList.remove("animated");
      const paragraphs = valueSection.querySelectorAll("p");
      paragraphs.forEach((p) => {
       p.style.opacity = "0";
       p.style.transform = "translateY(20px)";
       p.style.transition = "none";
      });
     }
    },
   });
  }
 }

 // Handle slide functionality
 function goToSlide(slideNumber) {
  // Remove all classes first
  connectionSlides.forEach((slide) => {
   slide.classList.remove("active", "previous");
  });

  // Add active class to current slide
  const targetSlide = document.querySelector(
   `.connection-section .slide[data-slide="${slideNumber}"]`
  );

  // Get previous slide
  const prevSlideNumber = slideNumber === 1 ? totalSlides : slideNumber - 1;
  const prevSlide = document.querySelector(
   `.connection-section .slide[data-slide="${prevSlideNumber}"]`
  );

  prevSlide.classList.add("previous");
  targetSlide.classList.add("active");

  currentSlide = slideNumber;
 }

 // Next button click handler
 if (nextButton) {
  nextButton.addEventListener("click", function () {
   const nextSlide = currentSlide === totalSlides ? 1 : currentSlide + 1;
   goToSlide(nextSlide);
  });
 }

 // Handle scroll events
 let lastScrollTop = 0;
 let wheelTimeout;
 let lastWheelTime = 0;
 let wheelEnabled = true; // Add a flag to track if wheel events should be processed
 const wheelCooldown = 500; // Reduced cooldown for better responsiveness

 window.addEventListener(
  "wheel",
  (e) => {
   // Get current scroll position
   const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
   const scrollingDown = e.deltaY > 0;

   // Handle scrolling in connection section
   if (isConnectionContentVisible) {
    const connectionScrollTop = connectionSection.scrollTop;
    const connectionScrollHeight = connectionSection.scrollHeight;
    const connectionClientHeight = connectionSection.clientHeight;

    // Check if we're at the bottom of the connection section
    if (
     scrollingDown &&
     Math.abs(
      connectionScrollTop + connectionClientHeight - connectionScrollHeight
     ) < 2 &&
     !isAnimating &&
     !isEndingContentVisible
    ) {
     e.preventDefault();
     showEndingSection();
     return;
    }
    // Only prevent default and transition when at top and scrolling up
    else if (!scrollingDown && connectionScrollTop <= 10) {
     // Only prevent and transition when at top and scrolling up
     if (!isAnimating) {
      e.preventDefault();
      wheelEnabled = true; // Ensure wheel is enabled when scrolling up

      // Show building section first, then hide connection section
      buildingSection.style.display = "block";
      gsap.to(buildingSection, {
       opacity: 1,
       duration: 0.3,
       onStart: () => {
        isBuildingContentVisible = true;
        // Start building section animations
        revealTexts.forEach((text) => {
         text.classList.add("animate");
        });
        floatingImages.forEach((img, index) => {
         setTimeout(() => {
          img.classList.add("visible");
         }, index * 200);
        });
       },
      });

      // Hide connection section with slight delay
      gsap.to(connectionSection, {
       opacity: 0,
       duration: 0.3,
       delay: 0.1,
       onComplete: () => {
        connectionSection.style.display = "none";
        isConnectionContentVisible = false;
        isAnimating = false;
       },
      });
     }
    }
    // Reset wheel enabled when we're back at the top of the connection section
    if (connectionScrollTop <= 10) {
     wheelEnabled = true;
    }
    return; // Allow normal scrolling within connection section
   }

   // Handle scrolling in ending section
   if (isEndingContentVisible) {
    const endingSectionScrollTop = endingSection.section.scrollTop;

    // Only prevent default and transition when at the top and scrolling up
    if (!scrollingDown && endingSectionScrollTop <= 10 && !isAnimating) {
     e.preventDefault();
     endingSection.hide();
     isEndingContentVisible = false;
     showConnectionSection();
     setTimeout(() => {
      connectionSection.scrollTop = connectionSection.scrollHeight;
     }, 100);
    }
    return; // Allow normal scrolling within ending section
   }

   // Handle other transitions
   if (scrollingDown && wheelEnabled) {
    // Transition to building section from customer content
    if (
     isCustomerContentVisible &&
     !isBuildingContentVisible &&
     !isConnectionContentVisible &&
     !isEndingContentVisible &&
     window.innerHeight + scrollTop >=
      document.documentElement.scrollHeight - 50
    ) {
     e.preventDefault();
     showBuildingSection();
     gsap.to(customerContent, {
      opacity: 0,
      y: -50,
      duration: 0.3,
      pointerEvents: "none",
     });
    }
    // Transition to connection section from building section
    else if (
     isBuildingContentVisible &&
     !isConnectionContentVisible &&
     !isEndingContentVisible
    ) {
     e.preventDefault();
     showConnectionSection();
    }
   } else {
    // Scrolling up transitions - always enable wheel when scrolling up
    wheelEnabled = true;

    if (isEndingContentVisible) {
     const endingSectionScrollTop = endingSection.section.scrollTop;
     const heroRect = endingSection.heroContent.getBoundingClientRect();

     if (endingSectionScrollTop === 0 && heroRect.top >= 0) {
      e.preventDefault();
      endingSection.hide();
      isEndingContentVisible = false;
      showConnectionSection();
      setTimeout(() => {
       connectionSection.scrollTop = connectionSection.scrollHeight;
      }, 100);
     }
    } else if (
     isBuildingContentVisible &&
     !isConnectionContentVisible &&
     !isEndingContentVisible
    ) {
     e.preventDefault();
     hideBuildingSection();
     gsap.to(customerContent, {
      opacity: 1,
      y: 0,
      duration: 0.5,
      pointerEvents: "auto",
      onComplete: () => {
       // Reset wheel state when customer content is visible again
       wheelEnabled = true;
      },
     });
    } else if (
     isCustomerContentVisible &&
     !isBuildingContentVisible &&
     !isConnectionContentVisible &&
     !isEndingContentVisible &&
     scrollTop <= 0
    ) {
     e.preventDefault();
     isCustomerContentVisible = false;
     gsap.to(customerContent, {
      opacity: 0,
      y: 50,
      duration: 0.3,
      pointerEvents: "none",
      onComplete: () => {
       document.body.style.overflow = "hidden";
       window.scrollTo(0, 0);
       wheelEnabled = true; // Make sure wheel is enabled when returning to the beginning
      },
     });
    }
   }
  },
  { passive: false }
 );

 // Reset on page reload
 window.addEventListener("beforeunload", () => {
  tl.kill();
  ScrollTrigger.getAll().forEach((st) => st.kill());
 });

 // Ending Section Code
 const endingSection = {
  textStates: [
   {
    h3: "Leveraging data and insights to anticipate needs",
    h2: "Deep customer understanding",
   },
   {
    h3: "Creating relevance and emotional connections",
    h2: "Personalised experiences",
   },
   {
    h3: "Encouraging meaningful interactions beyond purchases",
    h2: "A sense of community",
   },
  ],
  currentState: 0,
  isAnimating: false,
  animationInterval: null,
  init() {
   this.section = document.querySelector(".ending-section");
   this.contentBelow = this.section.querySelectorAll(".quote");
   this.deepUnderstandingSection = this.section.querySelector(
    ".deep-understanding"
   );
   this.understandingText = this.section.querySelector(".understanding-text");
   this.h3Element = this.section.querySelector(".understanding-text h3");
   this.h2Element = this.section.querySelector(".understanding-text h2");
   this.heroContent = this.section.querySelector(".hero-content");
   this.footer = this.section.querySelector("footer");
   this.footerText = this.footer.querySelector("h2");

   // Set initial text
   this.h3Element.textContent = this.textStates[this.currentState].h3;
   this.h2Element.textContent = this.textStates[this.currentState].h2;
   this.understandingText.classList.add("slide-active");

   // Setup intersection observer
   this.setupObserver();
  },
  show() {
   console.log("Showing ending section");

   // Make sure section is visible immediately
   this.section.style.display = "block";
   this.section.style.opacity = "1";

   // Ensure quote section is properly visible
   const quoteSection = this.section.querySelector(".quote");
   if (quoteSection) {
    quoteSection.style.visibility = "visible";
    quoteSection.style.opacity = "1";
    quoteSection.style.display = "block";
   }

   // Ensure value section is visible
   const valueSection = this.section.querySelector(".value");
   if (valueSection) {
    valueSection.style.visibility = "visible";
    valueSection.style.opacity = "1";
    valueSection.style.display = "block";
   }

   // Ensure footer is visible
   if (this.footer) {
    this.footer.style.visibility = "visible";
    this.footer.style.opacity = "1";
   }

   // Reset and reinitialize all observers
   this.setupQuoteScrollObserver();
   setupValueSectionObserver();

   // First animate hero content
   this.heroContent.classList.add("visible");

   // Animate hero description text word by word
   const heroDescription = this.section.querySelector(".hero-description h2");
   if (heroDescription) {
    const words = heroDescription.querySelectorAll(".word");
    words.forEach((word) => {
     word.classList.add("animate");
    });
   }

   // Show deep understanding section
   const deepUnderstanding = this.section.querySelector(".deep-understanding");
   if (deepUnderstanding) {
    deepUnderstanding.style.opacity = "1";
    deepUnderstanding.style.transform = "translateY(0)";
    this.startAutoAnimation();
   }

   console.log("Ending section display complete");
  },
  hide() {
   // Clear auto-animation interval
   if (this.animationInterval) {
    clearInterval(this.animationInterval);
    this.animationInterval = null;
   }

   gsap.to(this.section, {
    opacity: 0,
    duration: 0.3,
    onComplete: () => {
     this.section.style.display = "none";

     // Reset both quote and value section animation states
     const quoteSection = this.section.querySelector(".quote");
     const valueSection = this.section.querySelector(".value");

     if (quoteSection) {
      quoteSection.classList.remove("animation-completed");
      const words = quoteSection.querySelectorAll(".word");
      words.forEach((word) => {
       word.style.opacity = "0";
       word.style.transform = "translateY(20px)";
       word.style.transition = "none";
      });
     }

     if (valueSection) {
      valueSection.classList.remove("animated");
      const paragraphs = valueSection.querySelectorAll("p");
      paragraphs.forEach((p) => {
       p.style.opacity = "0";
       p.style.transform = "translateY(20px)";
       p.style.transition = "none";
      });
     }
    },
   });
  },
  startAutoAnimation() {
   // Clear any existing interval
   if (this.animationInterval) {
    clearInterval(this.animationInterval);
   }

   // Set up new interval
   this.animationInterval = setInterval(() => {
    const nextState = (this.currentState + 1) % this.textStates.length;
    this.updateTextContent(nextState);
   }, 3500); // Change text every 3.5 seconds instead of 5 seconds
  },
  updateTextContent(nextState) {
   if (this.isAnimating) return;
   this.isAnimating = true;

   this.understandingText.classList.add("slide-out");
   this.understandingText.classList.remove("slide-active");

   setTimeout(() => {
    this.currentState = nextState;
    this.h3Element.textContent = this.textStates[this.currentState].h3;
    this.h2Element.textContent = this.textStates[this.currentState].h2;

    this.understandingText.classList.remove("slide-out");
    this.understandingText.classList.add("slide-in");

    void this.understandingText.offsetWidth;

    this.understandingText.classList.remove("slide-in");
    this.understandingText.classList.add("slide-active");

    setTimeout(() => {
     this.isAnimating = false;

     // If we've reached the final state, start the content sequence - but don't animate quote again
     if (this.currentState === this.textStates.length - 1) {
      // First show the value section
      const valueSection = this.section.querySelector(".value");
      if (valueSection) {
       valueSection.classList.add("visible");

       // Make sure footer is visible
       setTimeout(() => {
        const footer = this.section.querySelector("footer");
        if (footer && !footer.classList.contains("visible")) {
         footer.style.visibility = "visible";
         gsap.to(footer, {
          opacity: 1,
          duration: 0.3,
         });
        }
       }, 1000);
      }
     }
    }, 400); // Reduced from 600ms to 400ms
   }, 400); // Reduced from 600ms to 400ms
  },
  animateWords(element, delay = 100) {
   if (!element) return;

   const text = element.textContent.trim();
   const words = text.split(" ").filter((word) => word.length > 0);
   element.innerHTML = words
    .map(
     (word, index) =>
      `<span style="animation-delay: ${index * delay}ms">${word}</span>`
    )
    .join("\u00A0");

   setTimeout(() => {
    element.classList.add("animate");
   }, 100);
  },
  showFooter() {
   if (this.footer) {
    setTimeout(() => {
     this.footer.classList.add("visible");
    }, 500);
   }
  },
  onQuoteAnimationComplete(quoteElement) {
   const words = quoteElement.querySelectorAll("span");
   const lastWord = words[words.length - 1];

   const lastWordDelay = parseFloat(lastWord.style.animationDelay);
   const animationDuration = 500;
   const totalTime = lastWordDelay + animationDuration + 500;

   setTimeout(() => this.showFooter(), totalTime);
  },
  setupObserver() {
   const observerOptions = {
    root: null,
    rootMargin: "0px",
    threshold: 0.1,
   };

   const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
     if (entry.isIntersecting) {
      if (entry.target.classList.contains("hero-content")) {
       entry.target.classList.add("visible");
      }
      observer.unobserve(entry.target);
     }
    });
   }, observerOptions);

   observer.observe(this.heroContent);
  },
  setupQuoteScrollObserver() {
   const quoteSection = document.querySelector(".ending-section .quote");
   if (!quoteSection) return;

   // Initialize the words if not done already
   const quoteText = quoteSection.querySelector("h2");
   if (quoteText && !quoteText.querySelector(".word")) {
    const text = quoteText.textContent.trim();
    const words = text.split(" ").filter((word) => word.length > 0);
    quoteText.innerHTML = words
     .map((word, index) => `<span class="word">${word}</span>`)
     .join(" ");
   }

   // Get all words and ensure proper initial state
   const words = quoteSection.querySelectorAll(".word");
   words.forEach((word) => {
    word.style.opacity = "0";
    word.style.transform = "translateY(20px)";
    word.style.transition = "none";
   });

   // Create a new Intersection Observer with more sensitive threshold
   const observer = new IntersectionObserver(
    (entries) => {
     entries.forEach((entry) => {
      if (
       entry.isIntersecting &&
       !quoteSection.classList.contains("animation-completed")
      ) {
       // Ensure the section is visible
       quoteSection.style.visibility = "visible";
       quoteSection.style.opacity = "1";
       quoteSection.style.display = "block";

       // Animate words with improved timing
       words.forEach((word, index) => {
        void word.offsetWidth; // Force reflow

        word.style.transition = "opacity 0.5s ease, transform 0.5s ease";
        word.style.transitionDelay = `${index * 0.05}s`;

        requestAnimationFrame(() => {
         word.style.opacity = "1";
         word.style.transform = "translateY(0)";
        });
       });

       quoteSection.classList.add("animation-completed");
       observer.unobserve(entry.target);
      }
     });
    },
    {
     threshold: [0, 0.1, 0.2, 0.3, 0.4, 0.5], // Multiple thresholds for better detection
     rootMargin: "-5% 0px", // Smaller margin to trigger earlier
    }
   );

   // Start observing the quote section
   observer.observe(quoteSection);
  },
 };

 // Initialize ending section
 endingSection.init();

 // Now also check if we should directly show the endingSection based on hash fragment
 if (window.location.hash === "#end") {
  console.log("Hash fragment detected, directly showing ending section");

  // Immediately make all ending section elements visible
  setTimeout(() => {
   // Force the endingSection and all its components to be displayed
   endingSection.show();

   // Set the customer content visible
   isCustomerContentVisible = true;

   // Set the ending section visible
   isEndingContentVisible = true;

   // Make sure we're at the top
   window.scrollTo(0, 0);

   console.log("Direct navigation to ending section complete");
  }, 200);
 }

 // Make quote visible on page load but don't animate words
 setTimeout(() => {
  const quote = document.querySelector(".ending-section .quote");
  if (quote) {
   quote.style.visibility = "visible";
   quote.style.opacity = "1";
   quote.style.display = "block";

   // Initialize the words but keep them invisible
   const quoteText = quote.querySelector("h2");
   if (quoteText && !quoteText.querySelector(".word")) {
    const text = quoteText.textContent.trim();
    const words = text.split(" ").filter((word) => word.length > 0);
    quoteText.innerHTML = words
     .map(
      (word, index) =>
       `<span class="word" style="animation-delay: ${
        index * 0.05
       }s">${word}</span>`
     )
     .join(" ");
   }
  }
 }, 1000);

 // Send a message to the parent window that the engagement page has loaded
 if (window.parent && window.parent !== window) {
  window.parent.postMessage(
   {
    type: "engagementLoaded",
   },
   "*"
  );
 }

 // Add message listener to handle scrolling to specific sections
 window.addEventListener("message", (event) => {
  if (event.data && event.data.type === "scrollToSection") {
   const sectionId = event.data.sectionId;
   const isDirectNavigation = event.data.directNavigation === true;

   console.log("Received scrollToSection message:", {
    sectionId,
    isDirectNavigation,
   });

   // Special case for scrolling to the building emotional connection section
   if (sectionId === "building") {
    console.log("Processing building section navigation request");

    // Hide journey container and show customer content
    const journeyContainer = document.querySelector(".journey-container");
    if (journeyContainer) {
     journeyContainer.style.display = "none";
    }

    // Make sure customer content is visible
    if (customerContent) {
     customerContent.style.display = "block";
     customerContent.style.visibility = "visible";
     customerContent.style.opacity = "1";
     customerContent.style.transform = "translateY(0)";
     customerContent.style.pointerEvents = "auto";
     isCustomerContentVisible = true;
    }

    // Show building section
    showBuildingSection();

    // Make sure scrolling works
    document.body.style.overflow = "auto";
    document.documentElement.style.overflow = "auto";

    // Scroll to top
    window.scrollTo(0, 0);
   }
   // Special case for scrolling to the sustainable engagement strategy section
   else if (sectionId === "end") {
    console.log("Processing end section navigation request");

    // For direct navigation, we need to be even more aggressive
    if (isDirectNavigation) {
     console.log("Direct navigation mode active");

     // Immediately hide everything except ending section
     const allElements = document.querySelectorAll(
      "body > *:not(main), main > *:not(.ending-section)"
     );
     allElements.forEach((el) => {
      el.style.display = "none";
     });

     // Hide journey container and any animation elements
     const journeyContainer = document.querySelector(".journey-container");
     if (journeyContainer) {
      journeyContainer.style.display = "none";
      journeyContainer.style.visibility = "hidden";
     }

     // Kill all GSAP animations and ScrollTrigger instances
     if (window.gsap && window.gsap.killTweensOf) {
      window.gsap.killTweensOf("*");
     }
     if (window.ScrollTrigger && window.ScrollTrigger.getAll) {
      window.ScrollTrigger.getAll().forEach((trigger) => trigger.kill());
     }

     // Make sure document is scrollable
     document.body.style.overflow = "auto";
     document.documentElement.style.overflow = "auto";

     // Ensure the ending section is visible and at the top
     window.scrollTo(0, 0);
     document.body.scrollTop = 0;
     document.documentElement.scrollTop = 0;

     // Force display ending section
     const endingSectionEl = document.querySelector(".ending-section");
     if (endingSectionEl) {
      endingSectionEl.style.display = "block";
      endingSectionEl.style.visibility = "visible";
      endingSectionEl.style.opacity = "1";

      // Show all ending section content
      const endingSectionContent = endingSectionEl.querySelectorAll("*");
      endingSectionContent.forEach((el) => {
       el.style.visibility = "visible";
       el.style.opacity = "1";
       el.style.display = "block";
      });
     }

     // Make sure customer content is fully visible
     if (customerContent) {
      customerContent.style.display = "block";
      customerContent.style.visibility = "visible";
      customerContent.style.opacity = "1";
      customerContent.style.transform = "translateY(0)";
      customerContent.style.pointerEvents = "auto";
      isCustomerContentVisible = true;
     }

     // Show ending section with special flag for direct navigation
     showEndingSection(true);

     console.log("Direct navigation to end section completed");
    } else {
     // Standard navigation - hide journey container
     const journeyContainer = document.querySelector(".journey-container");
     if (journeyContainer) {
      journeyContainer.style.display = "none";
     }

     // Hide ScrollTrigger elements
     const scrollTriggerElements = document.querySelectorAll(
      "[data-scrolltrigger]"
     );
     scrollTriggerElements.forEach((el) => {
      el.style.display = "none";
     });

     // Hide other sections
     const mainContent = document.querySelector("main");
     if (mainContent) {
      Array.from(mainContent.children).forEach((child) => {
       if (!child.classList.contains("ending-section")) {
        child.style.display = "none";
       }
      });
     }

     // Make sure customer content is visible
     document.body.style.overflow = "auto";

     // Show customer content
     if (customerContent) {
      customerContent.style.opacity = 1;
      customerContent.style.transform = "translateY(0)";
      customerContent.style.pointerEvents = "auto";
      isCustomerContentVisible = true;
     }

     // Show ending section
     showEndingSection(true);

     // Make sure we're at the top
     window.scrollTo(0, 0);
    }
   } else {
    // Find the journey card with matching ID
    const journeyCards = document.querySelectorAll(".journey-card");
    journeyCards.forEach((card, index) => {
     const cardNumber = card.querySelector(".card-number");
     if (cardNumber && cardNumber.textContent.includes(`(${sectionId})`)) {
      // First make sure customer content is visible
      showCustomerContent();

      // Scroll to the card
      setTimeout(() => {
       // Scroll to the detailed section
       const detailedSections = document.querySelectorAll(".detailed-section");
       if (detailedSections && detailedSections[index]) {
        detailedSections[index].scrollIntoView({ behavior: "smooth" });
       }
      }, 500);
     }
    });
   }

   // Send acknowledgment back to prevent channel closure error
   if (event.source) {
    event.source.postMessage({ type: "scrollToSectionAck" }, event.origin);
   }
  }
 });

 function setupValueSectionObserver() {
  const valueSection = document.querySelector(".ending-section .value");
  if (!valueSection) return;

  const paragraphs = valueSection.querySelectorAll("p");

  // Set initial state
  paragraphs.forEach((p) => {
   p.style.opacity = "0";
   p.style.transform = "translateY(20px)";
   p.style.transition = "none";
  });

  const observer = new IntersectionObserver(
   (entries) => {
    entries.forEach((entry) => {
     if (entry.isIntersecting && !valueSection.classList.contains("animated")) {
      // Add animation to each paragraph with staggered delays
      paragraphs.forEach((p, index) => {
       // Force a reflow
       void p.offsetWidth;

       // Set up the transition
       p.style.transition = "opacity 0.8s ease, transform 0.8s ease";
       p.style.transitionDelay = `${index * 0.2}s`;

       // Trigger the animation
       setTimeout(() => {
        p.style.opacity = "1";
        p.style.transform = "translateY(0)";
       }, 50);
      });

      // Mark as animated
      valueSection.classList.add("animated");

      // Cleanup
      observer.unobserve(entry.target);
     }
    });
   },
   {
    threshold: 0.3, // Trigger when 30% of the element is visible
    rootMargin: "-10% 0px",
   }
  );

  observer.observe(valueSection);
 }

 function showEndingSection(directNavigation = false) {
  console.log("showEndingSection called, directNavigation:", directNavigation);

  if ((!isEndingContentVisible && !isAnimating) || directNavigation) {
   isAnimating = true;
   isEndingContentVisible = true;

   // When coming from direct navigation, we need immediate display without any transitions
   if (directNavigation) {
    console.log("Direct navigation mode: forcing immediate display");

    // Immediately hide all other content
    if (connectionSection) {
     connectionSection.style.display = "none";
     isConnectionContentVisible = false;
    }

    if (buildingSection) {
     buildingSection.style.display = "none";
     isBuildingContentVisible = false;
    }

    // Kill all GSAP animations that might be running
    if (window.gsap && window.gsap.killTweensOf) {
     window.gsap.killTweensOf("*");
    }

    // Force immediate display of ending section and all its components
    endingSection.section.style.display = "block";
    endingSection.section.style.visibility = "visible";
    endingSection.section.style.opacity = "1";

    // Ensure all child elements are visible
    const allEndingSectionElements =
     endingSection.section.querySelectorAll("*");
    allEndingSectionElements.forEach((el) => {
     el.style.transition = "none"; // Disable transitions
     el.style.visibility = "visible";
     el.style.opacity = "1";

     // For elements that need to be displayed as blocks
     if (
      el.tagName === "SECTION" ||
      el.tagName === "DIV" ||
      el.classList.contains("quote") ||
      el.classList.contains("value") ||
      el.classList.contains("hero-content") ||
      el.classList.contains("deep-understanding")
     ) {
      el.style.display = "block";
     }
    });

    // Specifically ensure these sections are visible
    const valueSection = endingSection.section.querySelector(".value");
    if (valueSection) {
     valueSection.style.visibility = "visible";
     valueSection.style.opacity = "1";
     valueSection.style.display = "block";

     // Make paragraphs visible
     const paragraphs = valueSection.querySelectorAll("p");
     paragraphs.forEach((p) => {
      p.style.transition = "none";
      p.style.opacity = "1";
      p.style.transform = "translateY(0)";
     });

     valueSection.classList.add("animated");
    }

    const quoteSection = endingSection.section.querySelector(".quote");
    if (quoteSection) {
     quoteSection.style.visibility = "visible";
     quoteSection.style.opacity = "1";
     quoteSection.style.display = "block";

     // Make all words visible
     const words = quoteSection.querySelectorAll(".word");
     words.forEach((word) => {
      word.style.transition = "none";
      word.style.opacity = "1";
      word.style.transform = "translateY(0)";
     });

     quoteSection.classList.add("animation-completed");
    }

    const footer = endingSection.section.querySelector("footer");
    if (footer) {
     footer.style.visibility = "visible";
     footer.style.opacity = "1";
     footer.style.display = "block";
    }

    const heroContent = endingSection.section.querySelector(".hero-content");
    if (heroContent) {
     heroContent.classList.add("visible");
    }

    const deepUnderstanding = endingSection.section.querySelector(
     ".deep-understanding"
    );
    if (deepUnderstanding) {
     deepUnderstanding.style.opacity = "1";
     deepUnderstanding.style.transform = "translateY(0)";
    }

    // Make sure scrolling works
    document.body.style.overflow = "auto";
    document.documentElement.style.overflow = "auto";

    // Ensure we're at the top
    window.scrollTo(0, 0);

    // Complete animation immediately
    setTimeout(() => {
     isAnimating = false;
     console.log("Direct navigation display complete");
    }, 100);

    return;
   }

   // Regular transition logic for normal navigation within the page
   const connectionSectionVisible =
    connectionSection &&
    window.getComputedStyle(connectionSection).display !== "none" &&
    window.getComputedStyle(connectionSection).opacity !== "0";

   if (connectionSectionVisible) {
    // Hide connection section first
    gsap.to(connectionSection, {
     opacity: 0,
     duration: 0.3,
     onComplete: () => {
      connectionSection.style.display = "none";
      isConnectionContentVisible = false;

      // Show ending section
      endingSection.show();

      setTimeout(() => {
       isAnimating = false;
      }, 500);
     },
    });
   } else {
    // Direct display of ending section without transitions
    if (connectionSection) {
     connectionSection.style.display = "none";
     isConnectionContentVisible = false;
    }

    // Show ending section immediately
    endingSection.show();

    setTimeout(() => {
     isAnimating = false;
    }, 500);
   }
  }
 }

 // Setup intersection observer for fade-in animations
 const setupFadeInAnimations = () => {
  // Sections to animate
  const sections = [
   ".initial-section",
   ".journey-text",
   ".journey-cards",
   ".journey-conclusion",
  ];

  // Create intersection observer
  const observer = new IntersectionObserver(
   (entries) => {
    entries.forEach((entry) => {
     if (entry.isIntersecting) {
      // Animate the section
      const section = entry.target;

      // Different animations for different sections
      if (section.classList.contains("initial-section")) {
       // Fade in text container
       const textContainer = section.querySelector(".text-container");
       gsap.fromTo(
        textContainer,
        { opacity: 0, y: 30 },
        { opacity: 1, y: 0, duration: 1, ease: "power2.out" }
       );
      } else if (section.classList.contains("journey-text")) {
       // Stagger animate paragraphs
       const paragraphs = section.querySelectorAll("p");
       gsap.fromTo(
        paragraphs,
        { opacity: 0, y: 30 },
        {
         opacity: 1,
         y: 0,
         duration: 0.8,
         stagger: 0.2,
         ease: "power2.out",
        }
       );
      } else if (section.classList.contains("journey-cards")) {
       // Stagger animate cards
       const cards = section.querySelectorAll(".journey-card");
       gsap.fromTo(
        cards,
        { opacity: 0, y: 30 },
        {
         opacity: 1,
         y: 0,
         duration: 0.6,
         stagger: 0.1,
         ease: "power2.out",
        }
       );
      } else if (section.classList.contains("journey-conclusion")) {
       // Fade in conclusion paragraphs
       const paragraphs = section.querySelectorAll("p");
       gsap.fromTo(
        paragraphs,
        { opacity: 0, y: 30 },
        {
         opacity: 1,
         y: 0,
         duration: 0.8,
         stagger: 0.2,
         ease: "power2.out",
        }
       );
      }

      // Unobserve after animation
      observer.unobserve(section);
     }
    });
   },
   {
    threshold: 0.2, // Trigger when 20% of the element is visible
    rootMargin: "-50px", // Slight offset to trigger before fully in view
   }
  );

  // Set initial states
  sections.forEach((selector) => {
   const section = document.querySelector(selector);
   if (section) {
    // Set initial state
    if (section.classList.contains("journey-cards")) {
     const cards = section.querySelectorAll(".journey-card");
     gsap.set(cards, { opacity: 0, y: 30 });
    } else if (
     section.classList.contains("journey-text") ||
     section.classList.contains("journey-conclusion")
    ) {
     const paragraphs = section.querySelectorAll("p");
     gsap.set(paragraphs, { opacity: 0, y: 30 });
    } else {
     gsap.set(section.querySelector(".text-container"), { opacity: 0, y: 30 });
    }

    // Start observing
    observer.observe(section);
   }
  });
 };

 // Initialize fade-in animations
 setupFadeInAnimations();

 // Slide-in animation functionality for ALL elements
 // 1. Select text elements
 const textElements = document.querySelectorAll(
  "p, h1, h2, h3, h4, h5, h6, li, .card-title, .section-title, .section-subtitle, .main-text, .footer-text, .card-number, .popup-text, .banner-content .slide-content p, .banner-content .slide-content h3, .contact-text p, .reference-item p"
 );

 // 2. Select image elements
 const imageElements = document.querySelectorAll(
  "img:not(.journey-image):not(.fullscreen-image), .images-grid, .image-container, .hero-image, .consideration-image-container, .enrollment-images-grid, .star-badge, .accent-box"
 );

 // Special handling for scrolling-images-container
 const scrollingContainers = document.querySelectorAll(
  ".scrolling-images-container"
 );
 scrollingContainers.forEach((container) => {
  // Ensure no scrollbars are visible
  container.style.overflow = "hidden";
  container.style.msOverflowStyle = "none";
  container.style.scrollbarWidth = "none";

  // Add slide-in animation
  container.classList.add("slide-in-element");
  const delayClass = `delay-${Math.floor(Math.random() * 5) + 1}`;
  container.classList.add(delayClass);
 });

 // 3. Select other content elements
 const contentElements = document.querySelectorAll(
  ".section-content, .section-header, .journey-card, .card-inner, .banner-content, .blue-section, .container, .text-section, .text-wrapper, .content-with-badge, .contact-links, .references-section, button:not(.next-button), .footer-logo, .contact-icon, .accent-box"
 );

 // Add slide-in classes to text elements
 textElements.forEach((element, index) => {
  // Skip elements that are part of word-by-word animations
  if (
   !element.classList.contains("word") &&
   !element.parentElement.classList.contains("word")
  ) {
   element.classList.add("animationText");

   // Add delay classes in a pattern for staggered animation
   const delayClass = `delay-${(index % 5) + 1}`;
   element.classList.add(delayClass);
  }
 });

 // Add slide-in classes to image elements
 imageElements.forEach((element, index) => {
  // Skip elements that already have animations
  if (
   !element.classList.contains("floating-image") &&
   !element.classList.contains("visible")
  ) {
   element.classList.add("slide-in-image");

   // Add delay classes in a pattern for staggered animation
   const delayClass = `delay-${(index % 5) + 1}`;
   element.classList.add(delayClass);
  }
 });

 // Add slide-in classes to other content elements
 contentElements.forEach((element, index) => {
  // Skip elements that already have animations
  if (
   !element.classList.contains("reveal-text") &&
   !element.parentElement.classList.contains("word")
  ) {
   element.classList.add("slide-in-element");

   // Add delay classes in a pattern for staggered animation
   const delayClass = `delay-${(index % 5) + 1}`;
   element.classList.add(delayClass);
  }
 });

 // Create Intersection Observer to detect when elements enter viewport
 const observer = new IntersectionObserver(
  (entries) => {
   entries.forEach((entry) => {
    if (entry.isIntersecting) {
     entry.target.classList.add("visible");
     // Unobserve after animation to improve performance
     observer.unobserve(entry.target);
    }
   });
  },
  {
   root: null, // viewport
   threshold: 0.1, // trigger when 10% of the element is visible
   rootMargin: "0px 0px -50px 0px", // trigger a bit before the element enters viewport
  }
 );

 // Observe all elements with slide-in classes
 document
  .querySelectorAll(".animationText, .slide-in-image, .slide-in-element")
  .forEach((element) => {
   observer.observe(element);
  });

 // Add event listener for messages from the parent window
 window.addEventListener("message", function (event) {
  // Handle scroll to section message
  if (event.data && event.data.type === "scrollToSection") {
   const sectionId = event.data.sectionId;
   if (sectionId === "end") {
    // Hide journey container and show customer content
    const journeyContainer = document.getElementById("journey-container");
    if (journeyContainer) {
     journeyContainer.style.display = "none";
    }

    const customerContent = document.getElementById("customer-content");
    if (customerContent) {
     customerContent.style.display = "block";
    }

    // Enable scrolling
    document.body.style.overflow = "auto";
    document.documentElement.style.overflow = "auto";

    // Show the ending section
    const endingSection = document.getElementById("ending-section");
    if (endingSection) {
     endingSection.style.display = "block";
    }

    // Scroll to top after a short delay
    setTimeout(() => {
     window.scrollTo({
      top: 0,
      left: 0,
      behavior: "instant",
     });
    }, 100);
   }
  }

  // Handle initialize scrolling message
  if (event.data && event.data.type === "initializeScrolling") {
   // Ensure scrolling is properly initialized
   document.body.style.overflow = "auto";
   document.documentElement.style.overflow = "auto";

   // Force a small scroll to initialize the scrolling behavior
   window.scrollTo({
    top: 1,
    left: 0,
    behavior: "instant",
   });

   // Then scroll back to top
   setTimeout(() => {
    window.scrollTo({
     top: 0,
     left: 0,
     behavior: "instant",
    });
   }, 50);

   // Notify parent that scrolling is initialized
   window.parent.postMessage(
    {
     type: "scrollingInitialized",
    },
    "*"
   );
  }
 });

 // Ensure scrolling is properly initialized when the page loads
 document.addEventListener("DOMContentLoaded", function () {
  // Set proper overflow settings
  document.body.style.overflow = "auto";
  document.documentElement.style.overflow = "auto";

  // Notify parent that the page has loaded
  window.parent.postMessage(
   {
    type: "engagementLoaded",
   },
   "*"
  );
 });
});
