:root {
  --body-font: "Ralew<PERSON>", Helvetica;
  --title-font: "Playfair Display", Helvetica;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--body-font);
  background: white;
  color: #292b33;
  line-height: 1.5;
}

.app {
  overflow: hidden;
}

/* Hero Section */
.hero {
  position: relative;
  width: 100%;
  padding: 6rem 1rem;
}

/* Remove menu button styles */
.menu-button {
  display: none;
}

.hero-content {
  max-width: 1365px;
  margin: 0 auto;
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.8s ease, transform 0.8s ease;
}

.hero-text {
  text-align: center;
  margin-bottom: 4rem;
}

.hero-text .subtitle {
  font-family: var(--body-font);
  font-size: 26px;
  line-height: 1.23;
  color: #000000;
  font-weight: 400;
}

.hero-text h1 {
  font-family: var(--title-font);
  font-size: 40px;
  line-height: 1.14;
  font-weight: 400;
  font-style: normal;
  color: #000000;
}

.hero-main {
  display: flex;
  flex-direction: column;
  gap: 2.5rem;
}

@media (min-width: 1024px) {
  .hero-main {
    flex-direction: row;
    align-items: flex-start;
    justify-content: space-between;
  }

  .hero-description {
    width: 65%;
  }

  .hero-image {
    width: 35%;
  }
}

.hero-description h2 {
  font-family: var(--body-font);
  font-weight: 600;
  font-size: 50px;
  line-height: 1.174;
  color: #292B33;
}

.hero-image img {
  width: 100%;
  height: auto;
}

/* Deep Understanding Section */
.deep-understanding {
  position: relative;
  background: #6b6f70;
  overflow: hidden;
  width: 100vw;
  height: 600px;
  margin-left: calc(-50vw + 50%);
  margin-right: calc(-50vw + 50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.texture-overlay {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: url(../../public/texture-1-background.png) center center / cover;
  z-index: 1;
}

.deep-understanding .content {
  position: relative;
  max-width: 100%;
  height: 100%;
  padding: 4rem 1rem;
  color: white;
  opacity: 1;
  transform: none;
  display: flex;
  flex-direction: column;
  justify-content: center;
  z-index: 2;
}

.deep-understanding p {
  text-align: center;
  font-family: var(--body-font);
  font-size: 26px;
  font-weight: 400;
  line-height: 1.23;
  color: #FFFFFF;
  margin-bottom: 5rem;
  max-width: 1140px;
  margin-left: auto;
  margin-right: auto;
}

.understanding-text {
  text-align: center;
  max-width: 1140px;
  margin: 0 auto;
  transition: transform 0.8s ease, opacity 0.4s ease;
  opacity: 0;
  position: relative;
  z-index: 3;
  transform: translateX(100%);
}

.understanding-text h3 {
  font-family: var(--body-font);
  font-size: 35px;
  font-weight: 400;
  line-height: 1.174;
  margin-bottom: 1rem;
  color: #FFFFFF;
}

.understanding-text h2 {
  font-family: var(--title-font);
  font-size: 140px;
  line-height: 1.1;
  font-weight: 400;
  color: #FFFFFF;
}

/* Value Section */
.value {
  padding: 5rem 1rem;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.5s ease, visibility 0.5s ease;
}

.value.visible {
  opacity: 1;
  visibility: visible;
}

.card {
  max-width: 1140px;
  margin: 0 auto;
}

.card p {
  font-family: var(--body-font);
  font-size: 20px;
  line-height: 1.3;
  color: #292B33;
  font-weight: 400;
}

/* Quote Section */
.quote {
  padding: 5rem 1rem;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.5s ease, visibility 0.5s ease;
}

.quote.visible {
  opacity: 1;
  visibility: visible;
}

.quote h2 {
  max-width: 1140px;
  margin: 0 auto;
  font-family: var(--title-font);
  font-size: 50px;
  line-height: 1.1;
  font-weight: 400;
  color: #292B33;
}

.quote h2 span {
  opacity: 0;
  display: inline-block;
  transform: translateY(20px);
}

.quote h2.animate span {
  animation: fadeInWord 0.5s ease forwards;
}

@keyframes fadeInWord {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Footer */
footer {
  background: #BD6C6C;
  height: 255px;
  position: relative;
  overflow: hidden;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.8s ease, visibility 0.8s ease;
}

footer.visible {
  opacity: 1;
  visibility: visible;
}

footer h2 {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  font-family: var(--title-font);
  color: #F18F86;
  font-size: 140px;
  font-weight: 400;
  line-height: 1.1;
  white-space: nowrap;
  animation: scrollText 20s linear infinite;
  will-change: transform;
}

/* Animation Classes */
.visible {
  opacity: 1 !important;
  transform: translateY(0) !important;
}

/* Lock scrolling */
.scroll-lock {
  overflow: hidden;
}

.scroll-lock .deep-understanding {
  position: fixed;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  z-index: 100;
}

/* Content below banner */
.content-locked {
  opacity: 0;
  pointer-events: none;
  visibility: hidden;
  transition: opacity 0.5s ease, visibility 0.5s ease;
}

.content-unlocked {
  opacity: 1;
  pointer-events: auto;
  visibility: visible;
  transition: opacity 0.5s ease, visibility 0.5s ease;
}

/* Banner text animations */
.understanding-text {
  text-align: center;
  max-width: 1140px;
  margin: 0 auto;
  transition: transform 0.8s ease, opacity 0.4s ease;
  opacity: 0;
  position: relative;
  z-index: 3;
  transform: translateX(100%);
}

.understanding-text.slide-active {
  transform: translateX(0);
  opacity: 1;
  transition: transform 0.8s ease, opacity 0.4s ease;
}

.understanding-text.slide-out {
  transform: translateX(-100%);
  opacity: 0;
  transition: transform 0.8s ease, opacity 0.4s ease;
}

.understanding-text.slide-in {
  transform: translateX(100%);
  opacity: 0;
  transition: none;
}

@keyframes scrollText {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

/* Remove old slide classes */
.understanding-text.slide-left,
.understanding-text.slide-right {
  display: none;
}

/* Value and Quote sections */
.quote.visible {
  opacity: 1;
  transform: none;
}