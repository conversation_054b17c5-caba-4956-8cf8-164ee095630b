document.addEventListener('DOMContentLoaded', function() {
    if (typeof gsap !== 'undefined' && gsap.registerPlugin) {
        gsap.registerPlugin(ScrollTrigger);
    }


    gsap.from("h1", {
        y: 50,
        opacity: 0,
        duration: 1,
        ease: "power2.out",
        scrollTrigger: {
            trigger: "h1",
            start: "top 80%",
            toggleActions: "play none none none"
        }
    });

    gsap.from(".subtitle", {
        y: 30,
        opacity: 0,
        duration: 0.8,
        delay: 0.3,
        ease: "power2.out",
        scrollTrigger: {
            trigger: ".subtitle",
            start: "top 80%",
            toggleActions: "play none none none"
        }//shrijan
    });


    gsap.from(".highlight", {
        y: 30,
        opacity: 0,
        duration: 1,
        ease: "power2.out",
        scrollTrigger: {
            trigger: ".highlight",
            start: "top 80%",
            toggleActions: "play none none none"
        }
    });

  
    gsap.utils.toArray("p").forEach((p, i) => {
        gsap.from(p, {
            y: 30,
            opacity: 0,
            duration: 0.8,
            delay: i * 0.1,//shrijan
            ease: "power2.out",
            scrollTrigger: {
                trigger: p,
                start: "top 90%",
                toggleActions: "play none none none"
            }
        });
    });

 
    gsap.utils.toArray("h2").forEach((h2, i) => {
        gsap.from(h2, {
            x: -50,
            opacity: 0,
            duration: 0.8,
            ease: "power2.out",
            scrollTrigger: {
                trigger: h2,
                start: "top 80%",
                toggleActions: "play none none none"
            }
        });
    });


    gsap.from(".step", {
        y: 50,
        opacity: 0,
        duration: 0.6,
        stagger: 0.1,
        ease: "back.out(1)",
        scrollTrigger: {
            trigger: ".journey-steps",
            start: "top 80%",
            toggleActions: "play none none none"
        }
    });

  
    gsap.from(".section-divider", {
        scaleX: 0,
        opacity: 0,
        duration: 1,
        ease: "power2.out",
        scrollTrigger: {
            trigger: ".section-divider",
            start: "top 90%",
            toggleActions: "play none none none"
        }
    });

    gsap.from(".conclusion", {
        y: 50,
        opacity: 0,
        duration: 1,
        ease: "power2.out",
        scrollTrigger: {
            trigger: ".conclusion",
            start: "top 80%",
            toggleActions: "play none none none"
        }
    });

    
    gsap.from(".last-paragraph", {
        y: -50,
        opacity: 0,
        duration: 1,
        ease: "power2.out",
        scrollTrigger: {
            trigger: ".last-paragraph",
            start: "top 90%",
            toggleActions: "play none none none"
        }
    });


    const container = document.querySelector('.image-scroll-container');
    if (container && typeof gsap !== 'undefined') {
        const track = container.querySelector('.image-scroll-track');
        if (track) {
            const trackWidth = track.scrollWidth;
            const duration = trackWidth / 100;
            
            gsap.from(container, {
                y: 50,
                opacity: 0,
                duration: 1,
                ease: "power2.out",
                scrollTrigger: {
                    trigger: container,
                    start: "top 90%",
                    toggleActions: "play none none none"
                }
            });
            
            gsap.to(track, {
                x: () => -(trackWidth - window.innerWidth),
                duration: duration,
                ease: 'none',
                scrollTrigger: {
                    trigger: container,
                    start: 'top center',
                    end: () => `+=${trackWidth}`,
                    scrub: 1,
                    pin: true,
                    anticipatePin: 1,
                    invalidateOnRefresh: true
                }
            });
        }
    }
});