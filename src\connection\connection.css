:root {
  --primary-color: #476477;
  --text-color: #292b33;
  --background-color: #fff;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Raleway';
  background-color: var(--background-color);
  color: var(--text-color);
  line-height: 1.6;
}

.container {
  max-width: 1440px;
  margin: 0 auto;
  position: relative;
}

.main-content {
  padding: 57px 20px 64px;
}

/* Title Section */
.title-section {
  text-align: center;
  margin: 101px auto 48px;
  max-width: 1290px;
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.8s ease forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

h1 {
  font-family: 'Playfair Display', serif;
  font-size: 50px;
  line-height: 70px;
  margin-bottom: 16px;
  opacity: 0;
  animation: fadeIn 0.8s ease forwards 0.3s;
}

h2 {
  font-size: 35px;
  font-weight: 600;
  opacity: 0;
  animation: fadeIn 0.8s ease forwards 0.6s;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Text Sections */
.text-section {
  max-width: 1140px;
  margin: 0 auto 52px;
  color: #292B33;
}

.text-section .main-paragraph {
  font-size: 20px;
  line-height: 1.3;
  margin-bottom: 40px;
  font-weight: 400;
}

.text-section .key-points {
  margin: 30px 0;
}

.text-section .key-points p {
  font-size: 20px;
  line-height: 1.3;
  margin-bottom: 16px;
  font-weight: 400;
}

.text-section .key-points strong {
  font-weight: 600;
  margin-right: 8px;
}

.text-section p {
  font-size: 20px;
  line-height: 1.3;
  margin-bottom: 26px;
  font-weight: 400;
}

.text-section strong {
  font-weight: 600;
}

/* Blue Section */
.blue-section {
  background-color: var(--primary-color);
  position: relative;
  overflow: hidden;
  margin: 52px 0;
  width: 100vw;
  margin-left: calc(-50vw + 50%);
  padding: 0;
  height: 380px;
}

.blue-content {
  padding: 0;
  height: 100%;
  text-align: center;
  color: white;
  position: relative;
  max-width: 1440px;
  margin: 0 auto;
}

.slide {
  position: absolute;
  width: 100%;
  height: 100%;
  opacity: 0;
  transform: translateX(100%);
  transition: transform 0.8s ease, opacity 0.8s ease;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 81px 20px;
}

.slide.active {
  opacity: 1;
  transform: translateX(0);
}

.slide.previous {
  opacity: 0;
  transform: translateX(-100%);
}

.blue-content h3 {
  font-family: 'Playfair Display', serif;
  font-size: 35px;
  font-style: italic;
  font-weight: 700;
  max-width: 622px;
  margin: 0 auto;
}

.blue-content p {
  font-size: 26px;
  max-width: 947px;
  margin: 16px auto 0;
}

.next-button {
  position: absolute;
  right: 75px;
  top: 50%;
  transform: translateY(-50%);
  width: 60px;
  height: 60px;
  border: none;
  border-radius: 10px;
  background: transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.next-button img {
  width: 60px;
  height: 60px;
  filter: brightness(0) invert(1);
}

@media (max-width: 768px) {
  .main-content {
    padding: 40px 16px;
  }

  h1 {
    font-size: 40px;
    line-height: 56px;
  }

  h2 {
    font-size: 28px;
  }

  .text-section {
    font-size: 18px;
  }

  .blue-content h3 {
    font-size: 28px;
  }

  .blue-content p {
    font-size: 22px;
  }

  .next-button {
    right: 20px;
  }
}