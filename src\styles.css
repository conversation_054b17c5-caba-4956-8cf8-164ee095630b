:root {
 --web-body-2-font-family: "Raleway", Helvetica;
 --web-body-2-font-size: 20px;
 --web-body-2-font-weight: 400;
 --web-body-2-line-height: 26px;
}

* {
 margin: 0;
 padding: 0;
 box-sizing: border-box;
}

html {
 overflow-x: hidden;
 width: 100%;
}

body {
 font-family: var(--web-body-2-font-family);
 color: #292b33;
 width: 100%;
 overflow-x: hidden;
}

.container {
 max-width: 1440px;
 margin: 0 auto;
 display: flex;
 flex-direction: column;
 align-items: center;
 gap: 2rem;
 padding: 20px;
}

/* Introduction Section */
.introduction-section {
 display: flex;
 flex-direction: column;
 align-items: center;
 gap: 2rem;
 width: 100%;
}

.hero-section {
 position: relative;
 width: 100vw;
 height: 568px;
 overflow: hidden;
 margin-left: calc(-50vw + 50%);
 margin-right: calc(-50vw + 50%);
}

.hero-section img {
 width: 100%;
 height: 100%;
 object-fit: cover;
 min-width: 100vw;
}

.hero-content {
 position: absolute;
 bottom: 32px;
 left: 150px;
 display: flex;
 flex-direction: column;
 align-items: flex-start;
 color: white;
 width: calc(100% - 200px);
 max-width: 1240px;
}

.hero-content h2 {
 font-family: "Raleway", Helvetica;
 font-weight: 400;
 font-size: 26px;
 line-height: 1.174;
 margin-bottom: 8px;
 text-align: left;
 color: #ffffff;
}

.hero-text {
 width: 100%;
}

.hero-text h1 {
 font-family: "Playfair Display", Helvetica;
 font-weight: 400;
 font-size: 54px;
 line-height: 1.2;
 margin: 0;
 text-align: left;
 color: #ffffff;
 white-space: normal;
 max-width: 1200px;
}

.hero-text p {
 font-family: "Raleway", Helvetica;
 font-weight: 600;
 font-size: 1.875rem;
}

.harvard-study {
 margin-top: 7px;
 font-size: var(--web-body-2-font-size);
 line-height: var(--web-body-2-line-height);
 margin-bottom: 2rem;
}

.harvard-study a {
 text-decoration: underline;
 color: inherit;
}

.question-brands,
.question-loyalty {
 width: 100%;
 text-align: center;
 font-size: 35px;
 line-height: 35px;
 margin-bottom: 2rem;
}

.question-brands {
 max-width: 1000px;
}

.italic {
 font-family: "Playfair Display", Helvetica;
 font-style: italic;
 font-weight: 700;
}

.explanation,
.conclusion {
 font-size: var(--web-body-2-font-size);
 line-height: var(--web-body-2-line-height);
 margin-bottom: 2rem;
}

/* Main Content Section */
.main-content {
 width: 100%;
 padding: 2rem 20px;
}

.content-title {
 font-family: "Playfair Display", Helvetica;
 font-size: 100px;
 line-height: 95px;
 font-weight: 400;
 margin-bottom: 1.25rem;
}

.separator {
 height: 3px;
 background-color: #6b6f70;
 border: none;
 margin-bottom: 3rem;
}

.content-grid {
 display: flex;
 gap: 20px;
}

.left-column {
 width: 367px;
}

.section-header {
 display: flex;
 align-items: center;
 gap: 15px;
 margin-bottom: 2.5rem;
}

.section-header span {
 color: #6b6f70;
 font-family: "Raleway", Helvetica;
 font-weight: 600;
 font-size: 20px;
 line-height: 0.95em;
}

.section-header hr {
 flex-grow: 1;
 height: 2px;
 background-color: #6b6f70;
 border: none;
}

.topics {
 display: flex;
 flex-direction: column;
 gap: 27.58px;
 margin-bottom: 2.5rem;
}

.topic h3 {
 font-family: "Playfair Display", Helvetica;
 font-weight: 700;
 font-size: 20px;
 width: 363px;
 flex-direction: column;
 line-height: normal;
}

.conclusion-topics {
 display: flex;
 flex-direction: row;
 justify-content: space-between;
 gap: 20px;
 width: 100%;
}

.conclusion-topics .topic {
 width: 367px;
}

.conclusion-topics .topic h3 {
 font-family: "Playfair Display", Helvetica;
 font-size: 19.7px;
 line-height: 1.333em;
 margin-bottom: 5px;
 color: #000000;
}

.conclusion-topics .topic:first-child h3 {
 font-weight: 400;
}

.conclusion-topics .topic:first-child h3 span:last-child {
 font-family: "Raleway", Helvetica;
 font-weight: 400;
 font-size: 18px;
 line-height: 1.174em;
}

.conclusion-topics .topic:last-child h3 {
 font-family: "Playfair Display", Helvetica;
 font-weight: 700;
 font-size: 20px;
 line-height: 1.333em;
 color: #000000;
}

.playfair-bold {
 font-family: "Playfair Display", Helvetica;
 font-weight: 700;
}

.journey-steps {
 display: grid;
 grid-template-columns: repeat(2, 372px);
 gap: 1.25rem 1.75rem;
}

.step {
 position: relative;
 height: 54px;
 padding-left: 54px;
}

.step-number {
 position: absolute;
 left: 0;
 top: 2px;
 font-weight: 700;
 font-size: 19.7px;
}

.step-title {
 font-family: "Playfair Display", Helvetica;
 font-weight: 700;
 font-size: 20px;
 margin-bottom: 0.5rem;
}

.step-description {
 font-size: 18px;
}

@media (max-width: 1200px) {
 .container {
  padding: 0 20px;
 }

 .harvard-study,
 .explanation,
 .conclusion {
  width: 100%;
 }

 .content-grid {
  flex-direction: column;
 }

 .left-column,
 .right-column {
  width: 100%;
 }

 .journey-steps {
  grid-template-columns: 1fr;
 }
}

@media (max-width: 768px) {
 .hero-content {
  left: 40px;
  width: calc(100% - 80px);
 }

 .hero-content h2 {
  font-size: 22px;
 }

 .hero-text h1 {
  font-size: 38px;
  line-height: 1.3;
 }

 .question-brands,
 .question-loyalty {
  font-size: 28px;
  line-height: 1.3;
  padding: 0 20px;
 }

 .content-title {
  font-size: 60px;
  line-height: 65px;
 }

 .conclusion-topics {
  flex-direction: column;
 }

 .conclusion-topics .topic {
  width: 100%;
 }
}

/* Responsive styles for hero section and introduction */
@media (max-width: 1390px) {
 .hero-content {
  width: calc(100% - 160px);
 }

 .hero-text h1 {
  font-size: 48px;
 }
}

@media (max-width: 480px) {
 .hero-content {
  left: 20px;
  width: calc(100% - 40px);
 }

 .hero-content h2 {
  font-size: 20px;
 }

 .hero-text h1 {
  font-size: 30px;
 }

 .question-brands,
 .question-loyalty {
  font-size: 24px;
 }
}

/* Loyalty Section Styles */
:root {
 --body-font: "Raleway", Helvetica;
 --title-font: "Playfair Display", Helvetica;
 --primary-color: #292b33;
 --accent-color: #f18f86;
 --gray-color: #6b6f70;
}

#loyalty-content {
 width: 100%;
 overflow-x: hidden;
 transition: opacity 0.8s ease;
}

#loyalty-content .container {
 max-width: 1140px;
 width: 100%;
 padding: 0 20px;
 margin: 0 auto;
 gap: 2rem;
}

#loyalty-content .main {
 width: 100%;
}

@keyframes fadeIn {
 from {
  opacity: 0;
 }
 to {
  opacity: 1;
 }
}

.fade-in-animation {
 animation: fadeIn 0.8s ease forwards;
}

/* Loyalty Hero Section */
.loyalty-hero {
 padding: 128px 0;
 text-align: center;
}

.loyalty-hero-title {
 font-size: 50px;
 line-height: 70px;
 font-weight: normal;
}

.bold {
 font-family: var(--title-font);
 font-weight: 700;
}

/* Loyalty Introduction Section */
.loyalty-intro {
 padding: 80px 0;
}

.loyalty-body-text {
 font-family: var(--web-body-2-font-family);
 font-size: 20px;
 line-height: 26px;
 margin-bottom: 15px;
 margin-top: 86px;
}

.loyalty-question-block {
 text-align: center;
 margin: 50px 0;
}

.loyalty-question-intro {
 font-weight: bold;
 font-size: 20px;
 margin-bottom: 8px;
}

.loyalty-main-question {
 font-size: 35px;
 font-weight: 600;
}

.loyalty-italic {
 font-family: var(--title-font);
 font-style: italic;
 font-weight: 700;
}

/* Loyalty Statistics Section */
.loyalty-stats-grid {
 display: flex;
 flex-wrap: nowrap;
 gap: 32px;
 justify-content: space-between;
 flex-direction: row;
}

.loyalty-stat-card {
 flex: 1;
 width: calc(50% - 16px);
 min-width: 0;
}

.loyalty-stat-number {
 font-family: var(--title-font);
 font-size: 70px;
 font-weight: 700;
 color: var(--accent-color);
 line-height: 1.14;
 margin-bottom: 25px;
}

.loyalty-progress-bar {
 width: 100%;
 height: 15px;
 background-color: var(--gray-color);
 border-radius: 7.5px;
 margin-bottom: 25px;
}

.loyalty-progress {
 height: 100%;
 background-color: var(--accent-color);
 border-radius: 7.5px;
}

.loyalty-stat-description {
 font-size: 26px;
 line-height: 32px;
}

.loyalty-stat-description a {
 font-family: var(--web-body-2-font-family);
 font-size: 26px;
 line-height: 32px;
 color: inherit;
 text-decoration: underline;
}

/* Loyalty Benefits Section */
.loyalty-benefits {
 padding: 80px 0;
 overflow: visible;
}

.loyalty-benefits-list {
 width: 100%;
 margin-top: 15px;
 max-height: 70vh;
 overflow-y: auto;
 overflow-x: hidden;
 padding-right: 10px;
 -webkit-overflow-scrolling: touch;
}

.loyalty-benefit-item {
 width: 100%;
 opacity: 0;
 transform: translateY(40px);
 transition: opacity 0.8s ease-out, transform 0.8s ease-out;
 overflow: visible;
}

.loyalty-benefit-item.animated {
 opacity: 1;
 transform: translateY(0);
}

.loyalty-benefit-item-container {
 display: flex;
 flex-direction: column;
 width: 100%;
 overflow: visible;
}

.loyalty-benefit-header {
 display: flex;
 justify-content: space-between;
 align-items: center;
 padding: 20px 0;
 cursor: pointer;
}

.loyalty-benefit-title {
 font-family: var(--title-font);
 font-size: 36px;
 font-weight: normal;
 color: var(--primary-color);
}

.loyalty-benefit-toggle {
 font-size: 36px;
 color: var(--primary-color);
 transition: transform 0.5s ease;
}

.loyalty-benefit-content {
 font-family: var(--body-font);
 font-size: 20px;
 line-height: 1.3;
 color: #000000;
 height: 0;
 opacity: 0;
 visibility: hidden;
 padding: 0;
 transition: opacity 0.4s ease;
}

.loyalty-benefit-content[style*="display: block"] {
 height: auto;
 opacity: 1;
 visibility: visible;
}

.loyalty-benefit-divider {
 height: 1px;
 background-color: #dadada;
 margin: 12px 0;
 width: 100%;
}

/* Custom scrollbar for loyalty benefits list */
.loyalty-benefits-list::-webkit-scrollbar {
 width: 8px;
}

.loyalty-benefits-list::-webkit-scrollbar-track {
 background: #f1f1f1;
 border-radius: 4px;
}

.loyalty-benefits-list::-webkit-scrollbar-thumb {
 background: #888;
 border-radius: 4px;
}

.loyalty-benefits-list::-webkit-scrollbar-thumb:hover {
 background: #555;
}

/* Loyalty Advocacy Section */
.loyalty-advocacy {
 padding: 80px 0;
}

.loyalty-advocacy-grid {
 display: flex;
 gap: 32px;
 align-items: stretch;
}

.loyalty-advocacy-image {
 flex: 1;
 display: flex;
}

.loyalty-advocacy-image img {
 width: 100%;
 height: 100%;
 object-fit: cover;
 border-radius: 8px;
}

.loyalty-advocacy-content {
 flex: 1;
 display: flex;
 flex-direction: column;
 justify-content: flex-start;
}

.loyalty-advocacy-title {
 font-family: var(--title-font);
 font-size: 40px;
 font-weight: 700;
 margin-bottom: 24px;
 color: #292b33;
}

.loyalty-final {
 padding: 80px 0;
 text-align: center;
}

.loyalty-final-title {
 font-size: 45px;
 line-height: 1.2;
 font-weight: normal;
}

.loyalty-content-section {
 padding: 2rem 0;
 position: relative;
 overflow: visible;
}

.loyalty-content-text {
 max-width: 800px;
 margin: 0 auto;
 position: relative;
 z-index: 1;
 padding-bottom: 2rem;
 display: flex;
 flex-direction: column;
 align-items: center;
}

.loyalty-content-text p {
 font-size: 20px;
 line-height: 1.5;
 margin-bottom: 24px;
 width: 100%;
}

.loyalty-content-text .loyalty-statistic-badge {
 position: relative;
 right: auto;
 top: auto;
 transform: translateY(0);
 width: 191px;
 height: 191px;
 z-index: 10;
 cursor: pointer;
 background-color: transparent;
 transition: transform 0.3s ease;
 margin-right: 50px;
}

.loyalty-content-section .loyalty-statistic-badge {
 top: auto;
 right: auto;
 transform: none;
}

.loyalty-badge-star {
 position: absolute;
 top: 0;
 left: 0;
 width: 100%;
 height: 100%;
 z-index: -1;
}

.loyalty-badge-content {
 position: absolute;
 top: 0;
 left: 0;
 right: 0;
 bottom: 0;
 display: flex;
 flex-direction: column;
 align-items: center;
 justify-content: center;
 color: white;
 z-index: 1;
 text-align: center;
}

.loyalty-percentage {
 font-family: var(--title-font);
 font-size: 60px;
 font-weight: 700;
 line-height: 1;
 letter-spacing: -1px;
 margin-bottom: 5px;
}

.loyalty-text {
 font-family: var(--body-font);
 font-size: 24px;
 font-weight: 600;
 text-transform: lowercase;
 letter-spacing: -0.5px;
 line-height: 1.1;
}

.loyalty-popup-overlay {
 position: fixed;
 top: 0;
 left: 0;
 right: 0;
 bottom: 0;
 background-color: rgba(0, 0, 0, 0.7);
 z-index: 1000;
 display: none;
 opacity: 0;
 transition: opacity 0.3s ease;
}

.loyalty-popup {
 position: fixed;
 top: 50%;
 left: 50%;
 transform: translate(-50%, -50%);
 width: 80%;
 max-width: 850px;
 height: auto;
 min-height: 450px;
 background-color: white;
 border-radius: 50px;
 z-index: 1001;
 display: none;
 opacity: 0;
 transition: opacity 0.3s ease;
 box-shadow: 3px 3px 6.5px 0px rgba(0, 0, 0, 0.25);
 padding: 40px 50px 50px;
 margin: 0;
}

.loyalty-popup-content {
 padding: 0;
 display: flex;
 flex-direction: column;
 align-items: flex-start;
 justify-content: flex-start;
 position: relative;
 width: 100%;
 height: 100%;
}

.loyalty-popup-close {
 position: absolute;
 top: 30px;
 left: 30px;
 cursor: pointer;
 z-index: 5;
}

.loyalty-popup-close img {
 width: 18px;
 height: 18px;
 display: block;
}

.loyalty-popup-text {
 text-align: left;
 width: 100%;
 margin-top: 20px;
}

.loyalty-popup-percentage {
 font-family: var(--title-font);
 font-size: 196px;
 font-weight: 700;
 color: #f18f86;
 letter-spacing: -2px;
 line-height: 1.39;
 margin-bottom: 0;
 text-align: left;
 display: block;
}

.loyalty-popup-text p {
 font-size: 35px;
 line-height: 1.3;
}

/* Responsive styles for loyalty popup */
@media (max-width: 1024px) {
 .loyalty-popup {
  width: 90%;
  min-height: 400px;
  padding: 35px 40px 40px;
 }

 .loyalty-popup-percentage {
  font-size: 160px;
  line-height: 1.2;
 }

 .loyalty-popup-text p {
  font-size: 30px;
 }
}

@media (max-width: 1000px) {
 .final-text {
  font-size: 20px;
  line-height: 0.9;
 }
}

@media (max-width: 768px) {
 .loyalty-popup {
  width: 95%;
  min-height: 350px;
  padding: 30px 35px 35px;
  border-radius: 30px;
 }

 .loyalty-popup-percentage {
  font-size: 130px;
 }

 .loyalty-popup-text p {
  font-size: 26px;
 }

 .loyalty-popup-close {
  top: 20px;
  left: 20px;
 }
}

@media (max-width: 480px) {
 .loyalty-popup {
  width: 95%;
  min-height: 300px;
  padding: 25px 20px 30px;
  border-radius: 25px;
 }

 .loyalty-popup-percentage {
  font-size: 90px;
  line-height: 1.1;
 }

 .loyalty-popup-text p {
  font-size: 22px;
  line-height: 1.3;
 }

 .loyalty-popup-close {
  top: 15px;
  left: 15px;
 }

 .loyalty-popup-close img {
  width: 16px;
  height: 16px;
 }
}

@media (max-width: 360px) {
 .loyalty-popup {
  width: 95%;
  min-height: 280px;
  padding: 20px 15px 25px;
 }

 .loyalty-popup-percentage {
  font-size: 70px;
 }

 .loyalty-popup-text p {
  font-size: 20px;
 }
}

/* Ensure popup overlay covers entire screen */
.loyalty-popup-overlay {
 position: fixed;
 top: 0;
 left: 0;
 right: 0;
 bottom: 0;
 background-color: rgba(0, 0, 0, 0.7);
 z-index: 1000;
 display: none;
 opacity: 0;
 transition: opacity 0.3s ease;
}

.loyalty-statistic-badge:hover {
 transform: scale(1.05);
}

/* Responsive styles for loyalty section */
@media (max-width: 1024px) {
 .loyalty-advocacy-image,
 .loyalty-advocacy-content {
  flex: 1;
 }

 .loyalty-stats-grid {
  flex-direction: row;
  flex-wrap: wrap;
 }

 .loyalty-stat-card {
  width: 100%;
  flex: 0 0 100%;
 }
}

@media (max-width: 768px) {
 .journey-initial {
  flex-direction: column;
  gap: 1rem;
  text-align: center;
 }

 .title-left,
 .title-right,
 .fullscreen-title {
  font-size: 36px;
 }

 .fullscreen-title .title-part {
  font-size: 36px;
  color: #ffffff;
 }

 .journey-visual {
  width: 40px;
  height: 40px;
 }

 .title-spacer-large {
  width: 10rem;
 }

 /* Adjust loyalty benefits scroll height for mobile */
 .loyalty-benefits-list {
  max-height: 60vh;
 }
}

@media (max-width: 480px) {
 .title-left,
 .title-right,
 .fullscreen-title {
  font-size: 28px;
  white-space: normal;
  word-wrap: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
  padding: 0 10px;
 }

 .journey-visual {
  width: 30px;
  height: 30px;
 }

 .title-spacer-large {
  width: 5rem;
 }

 /* Further adjust loyalty benefits scroll height for small screens */
 .loyalty-benefits-list {
  max-height: 50vh;
 }
}

/* Add new media query for very small screens */
@media (max-width: 405px) {
 .title-left,
 .title-right,
 .fullscreen-title {
  font-size: 28px; /* Maintain font size */
  white-space: normal;
  word-wrap: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
  padding: 0 10px;
 }

 .brand-hero-text {
  font-size: 50px; /* Maintain font size */
  line-height: 1.2;
  white-space: normal;
  word-wrap: break-word;
  overflow-wrap: break-word;
  padding: 0 10px;
 }

 .fullscreen-title .title-part {
  font-size: 50px; /* Maintain font size */
  white-space: normal;
  word-wrap: break-word;
  overflow-wrap: break-word;
 }

 .journey-initial {
  padding: 0 10px;
 }

 .topic h3 {
  font-size: 20px;
  line-height: 1.4;
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: normal;
  padding-right: 10px;
 }

 .topic h3#advocacy-link,
 .topic h3#awareness-link {
  font-size: 20px;
  white-space: normal;
  word-wrap: break-word;
  overflow-wrap: break-word;
  width: 100%;
  padding-right: 10px;
 }

 .menu-item-title,
 .menu-item-subtitle {
  font-size: 20px;
  line-height: 1.4;
  white-space: normal;
  word-wrap: break-word;
  overflow-wrap: break-word;
 }
}

/* Make loyalty sections responsive with same layout as main content */
@media (max-width: 1200px) {
 #loyalty-content .container,
 #loyalty-content .loyalty-content-text {
  width: 100%;
  padding: 0 20px;
 }

 .loyalty-advocacy-grid {
  flex-direction: column;
 }

 .loyalty-advocacy-image,
 .loyalty-advocacy-content {
  width: 100%;
 }
}

.loyalty-bottom-section {
 padding: 50px 0;
 text-align: center;
 position: relative;
 cursor: pointer;
}

.loyalty-bottom-content {
 display: flex;
 flex-direction: column;
 align-items: center;
 justify-content: center;
}

.loyalty-bottom-title {
 font-family: var(--title-font);
 font-size: 40px;
 font-weight: 700;
 margin-bottom: 16px;
 color: #292b33;
 cursor: pointer;
 transition: color 0.3s ease;
}

.loyalty-bottom-title:hover {
 color: #000000;
}

.loyalty-bottom-subtitle {
 font-family: var(--title-font);
 font-size: 30px;
 font-weight: 400;
 margin-bottom: 24px;
 color: #292b33;
}

.loyalty-bottom-section .loyalty-arrow-icon {
 margin-top: 24px;
 cursor: pointer;
 user-select: none;
 display: flex;
 align-items: center;
 justify-content: center;
}

.loyalty-bottom-section .loyalty-arrow-icon img {
 display: block;
 pointer-events: none;
}

.loyalty-bottom-section .loyalty-arrow-icon:hover img {
 transform: translateY(5px);
}

.loyalty-statistic-badge {
 position: absolute;
 width: 191px;
 height: 191px;
 left: 100px;
 cursor: pointer;
 background-color: transparent;
 transition: transform 0.3s ease;
}

.loyalty-content-text p:last-child {
 margin-bottom: 0;
}

/* Environment Section Styles */
#environment-content {
 display: none;
 position: fixed;
 top: 0;
 left: 0;
 right: 0;
 bottom: 0;
 width: 100vw;
 height: 100vh;
 overflow: hidden;
 z-index: 1000;
 background-color: #ffffff;
 opacity: 1;
 transition: opacity 0.5s ease-out;
}

#environment-content.active {
 display: block;
}

.environment-section {
 position: absolute;
 top: 0;
 left: 0;
 right: 0;
 bottom: 0;
 width: 100vw;
 height: 100vh;
 overflow: hidden;
}

.environment-container {
 width: 100vw;
 height: 100vh;
 display: flex;
 flex-direction: column;
 align-items: center;
 justify-content: center;
 padding: 2rem;
 position: relative;
 background-color: #ffffff;
 z-index: 2;
}

/* Main text styles with animation properties */
.main-text {
 max-width: 1200px;
 text-align: center;
 padding: 0 1rem;
 transition: opacity 0.8s ease-out, transform 0.8s ease-out;
}

.main-text h1 {
 font-size: 50px;
 line-height: 1.4;
 font-weight: 400;
 margin-bottom: 1rem;
 color: #292b33;
}

.main-text p {
 font-size: 50px;
 line-height: 1.4;
 font-weight: 400;
 color: #292b33;
}

/* Media queries for main-text responsive behavior */
@media (max-width: 800px) {
 .main-text {
  max-width: 90%;
  padding: 0 10px;
 }

 .main-text h1 {
  font-size: clamp(28px, 5vw, 50px);
  line-height: 1.3;
  margin-bottom: 1rem;
  word-wrap: break-word;
  overflow-wrap: break-word;
 }

 .main-text p {
  font-size: clamp(28px, 5vw, 50px);
  line-height: 1.3;
  word-wrap: break-word;
  overflow-wrap: break-word;
 }
}

/* Scroll section styles */
.scroll-container {
 position: fixed;
 top: 0;
 left: 0;
 right: 0;
 bottom: 0;
 display: flex;
 width: 400vw;
 height: 100vh;
 transform: translateX(0);
 transition: transform 0.8s ease-out;
 will-change: transform;
 z-index: 1;
}

.scroll-section {
 width: 100vw;
 height: 100vh;
 flex-shrink: 0;
 display: flex;
 justify-content: center;
 align-items: center;
 padding: 40px 20px;
 opacity: 0;
 visibility: hidden;
 transition: opacity 0.8s ease-out, visibility 0.8s ease-out;
 background-size: cover;
 background-position: center;
 background-repeat: no-repeat;
}

.scroll-section.active {
 opacity: 1;
 visibility: visible;
}

.content-wrapper {
 max-width: 1440px;
 width: 100%;
 display: flex;
 flex-direction: column;
 align-items: center;
 gap: 15px;
 transform: translateY(50px);
 opacity: 0;
 transition: transform 0.8s ease-out, opacity 0.8s ease-out;
}

.scroll-section.active .content-wrapper {
 transform: translateY(0);
 opacity: 1;
}

.title {
 font-family: "Playfair Display", serif;
 font-weight: 700;
 font-size: 35px;
 line-height: 1.33;
 text-align: center;
 color: #ffffff;
}

.questions-container {
 width: 100%;
 display: flex;
 flex-direction: column;
 gap: 5px;
 text-align: center;
}

.question {
 font-family: "Raleway";
 font-weight: 600;
 font-size: 35px;
 line-height: 1.17;
 color: #ffffff;
}

.description {
 font-family: "Raleway";
 font-weight: 400;
 font-size: 26px;
 line-height: 1.23;
 margin-top: 10px;
 color: #ffffff;
}

/* Final scroll section styles */
#final-scroll {
 background: #ffffff;
 background-image: none;
}

#final-scroll .content-wrapper {
 max-width: 1440px;
 padding: 40px;
}

.final-content {
 max-width: 1200px;
 margin: 0 auto;
}

.final-text {
 font-family: "Raleway";
 font-weight: 400;
 font-size: 35px;
 line-height: 1.174;
 color: #292b33;
 margin-bottom: 2em;
}

.final-text:last-child {
 margin-bottom: 0;
}

/* Environment responsive styles */
@media (max-width: 1200px) {
 .environment-container .main-text h1,
 .environment-container .main-text p {
  font-size: 40px;
 }
}

@media (max-width: 1000px) {
 .final-text {
  font-family: "Raleway";
  font-weight: 400;
  font-size: 20px;
  line-height: 0.9;
  color: #292b33;
  margin-bottom: 2em;
 }
}

@media (max-width: 768px) {
 .environment-container .main-text h1,
 .environment-container .main-text p {
  font-size: 30px;
 }

 .title {
  font-size: 28px;
 }

 .question {
  font-size: 24px;
 }

 .description {
  font-size: 20px;
 }
}

.loyalty-arrow-icon {
 cursor: pointer;
 transition: transform 0.3s ease;
}

.loyalty-arrow-icon:hover {
 transform: translateY(5px);
}

/* Section-specific background styles */
#environments {
 background-image: url("../public/env.png");
}

#experiences {
 background-image: url("../public/exp.png");
}

#emotions {
 background-image: url("../public/emo.png");
}

/* Journey Section Styles */
#journey-content {
 position: fixed;
 top: 0;
 left: 0;
 width: 100vw;
 height: 100vh;
 background-color: #ffffff;
 z-index: 1000;
 overflow: hidden;
}

.journey-container {
 width: 100%;
 height: 100vh;
 position: relative;
 overflow: hidden;
}

.journey-background {
 position: absolute;
 top: 0;
 left: 0;
 width: 100%;
 height: 100%;
 background-color: #ffffff;
 transform-origin: center;
 z-index: 1;
}

.journey-content {
 position: relative;
 width: 100%;
 height: 100%;
 display: flex;
 align-items: center;
 justify-content: center;
 z-index: 2;
}

.journey-initial {
 display: flex;
 align-items: center;
 justify-content: center;
 gap: 2rem;
 z-index: 3;
}

.title-left,
.title-right {
 font-size: 50px;
 font-weight: 400;
 line-height: 1.4;
 color: #000000;
 white-space: nowrap;
 transition: opacity 0.8s ease, transform 0.8s ease;
}

.journey-visual {
 width: 60px;
 height: 60px;
 position: relative;
 overflow: hidden;
 z-index: 2;
 transition: opacity 0.8s ease, transform 0.8s ease;
}

.polygon-container {
 width: 100%;
 height: 100%;
 position: relative;
}

.polygon {
 position: absolute;
 width: 100%;
 height: 100%;
 clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
 will-change: transform;
}

.polygon-1 {
 background-color: black;
}

.polygon-2 {
 background-color: black;
 overflow: hidden;
}

.image-mask {
 position: absolute;
 width: 100%;
 height: 100%;
 overflow: hidden;
}

.journey-image {
 width: 100%;
 height: 100%;
 object-fit: cover;
 will-change: transform, opacity;
}

.journey-fullscreen {
 position: absolute;
 top: 0;
 left: 0;
 width: 100%;
 height: 100%;
 opacity: 0;
 visibility: hidden;
 z-index: 1;
}

.fullscreen-image {
 position: absolute;
 top: 0;
 left: 0;
 width: 100%;
 height: 100%;
 object-fit: cover;
}

.fullscreen-title {
 position: absolute;
 top: 50%;
 left: 50%;
 transform: translate(-50%, -50%);
 font-size: 50px;
 font-weight: 400;
 line-height: 1.4;
 text-align: center;
 color: #ffffff;
 display: flex;
 justify-content: center;
 align-items: center;
 flex-wrap: wrap;
 gap: 0.5rem;
 width: 100%;
 opacity: 0;
 visibility: hidden;
}

.fullscreen-title.from-awareness {
 opacity: 0;
 visibility: hidden;
}

.fullscreen-title.to-advocacy-split {
 opacity: 0;
 visibility: hidden;
}

.fullscreen-title.to-advocacy-joined {
 opacity: 0;
 visibility: hidden;
}

.title-text {
 white-space: nowrap;
}

.title-spacer-large {
 width: 20rem;
 display: inline-block;
}

/* Animation classes */
.animate-in .polygon-1 {
 transform: translateX(0);
}

.animate-in .polygon-2 {
 transform: translateX(0);
}

.animate-in .journey-image {
 transform: scale(1);
 opacity: 1;
}

/* Journey Responsive styles */
@media (max-width: 1200px) {
 .journey-visual {
  width: 50px;
  height: 50px;
 }
}

@media (max-width: 768px) {
 .journey-initial {
  flex-direction: column;
  gap: 1rem;
  text-align: center;
 }

 .title-left,
 .title-right,
 .fullscreen-title {
  font-size: 36px;
 }

 .journey-visual {
  width: 40px;
  height: 40px;
 }

 .title-spacer-large {
  width: 10rem;
 }
}

@media (max-width: 480px) {
 .title-left,
 .title-right,
 .fullscreen-title {
  font-size: 28px;
  white-space: normal;
  word-wrap: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
  padding: 0 10px;
 }

 .journey-visual {
  width: 30px;
  height: 30px;
 }

 .title-spacer-large {
  width: 5rem;
 }
}

.loyalty-title {
 font-family: "Playfair Display", Helvetica;
 font-weight: 400;
 font-size: 35px;
 line-height: 1.174;
 text-align: center;
 color: #292b33;
 margin-bottom: 32px;
 font-style: italic;
}

.loyalty-title strong {
 font-weight: 700;
}

.title-part {
 font-size: 48px;
 line-height: 1.2;
 margin-bottom: 15px;
 color: #292b33;
}

.subtitle-part {
 font-size: 42px;
 line-height: 1.3;
 color: #292b33;
 opacity: 0.9;
}

@media (max-width: 768px) {
 .title-part {
  font-size: 36px;
  margin-bottom: 10px;
 }

 .subtitle-part {
  font-size: 32px;
 }
}

/* Add styles for the animated title */
.awareness-title {
 font-family: var(--title-font);
 font-size: 35px;
 line-height: 1.4;
 margin-bottom: 10px;
 color: #292b33;
 opacity: 0;
 transform: translateY(20px);
}

.awareness-title.animate {
 opacity: 1;
 transform: translateY(0);
 transition: opacity 0.8s ease, transform 0.8s ease;
}

.awareness-subtitle {
 font-family: var(--title-font);
 font-size: 35px;
 line-height: 1.4;
 color: #292b33;
 opacity: 0;
 transform: translateY(20px);
}

.awareness-subtitle.animate {
 opacity: 1;
 transform: translateY(0);
 transition: opacity 0.8s ease 0.3s, transform 0.8s ease 0.3s;
}

@media (max-width: 768px) {
 .awareness-title,
 .awareness-subtitle {
  font-size: 28px;
  line-height: 1.3;
 }
}

@media (max-width: 480px) {
 .awareness-title,
 .awareness-subtitle {
  font-size: 24px;
  line-height: 1.2;
 }
}

/* Add specific styling for fullscreen journey title parts */
.fullscreen-title .title-part {
 color: #ffffff;
 font-size: 50px;
 line-height: 1.4;
 margin-bottom: 0;
}

.fullscreen-title .title-text {
 color: #ffffff;
}

/* Add styles for the journey scroll indicator */
.journey-scroll-indicator {
 position: absolute;
 bottom: 40px;
 left: 50%;
 transform: translateX(-50%);
 text-align: center;
 color: #000;
 z-index: 100;
 opacity: 1;
 transition: opacity 0.5s ease;
}

.journey-scroll-arrow {
 width: 20px;
 height: 20px;
 border-right: 2px solid #000;
 border-bottom: 2px solid #000;
 transform: rotate(45deg);
 margin: 0 auto 10px;
 animation: bounce 1.5s infinite;
}

.journey-scroll-text {
 font-family: "Raleway";
 font-size: 14px;
 opacity: 0.8;
}

@keyframes bounce {
 0%,
 100% {
  transform: rotate(45deg) translate(0, 0);
 }
 50% {
  transform: rotate(45deg) translate(0, 10px);
 }
}

/* Brand Section Styles */
.brand-section {
 width: 100%;
 margin: 0 auto;
 background-color: white;
}

/* Brand Content Fullscreen */
#brand-content-fullscreen {
 display: block;
 position: absolute;
 top: 0;
 left: 0;
 width: 100%;
 height: 100%;
 min-height: 100vh;
 background-color: white;
 overflow-y: auto;
 z-index: 999;
}

/* Hero Section */
.brand-hero {
 width: 100%;
 max-width: 1440px;
 padding: 284px 0;
 background-color: white;
 margin: 0 auto;
}

.brand-hero-content {
 max-width: 1290px;
 margin: 0 auto;
 text-align: center;
}

.brand-hero-text {
 font-family: "Playfair Display", serif;
 font-size: 50px;
 line-height: 70px;
}

/* Journey Section */
.brand-journey {
 width: 100%;
 max-width: 1440px;
 padding: 64px 0;
 margin: 0 auto;
}

.brand-journey-content {
 max-width: 1141px;
 margin: 0 auto;
 display: flex;
 flex-direction: column;
 gap: 50px;
 padding: 0 20px;
}

.brand-intro-text {
 font-size: 20px;
 line-height: 26px;
}

.brand-intro-text p {
 margin-bottom: 20px;
}

.brand-journey-cards {
 display: grid;
 grid-template-columns: repeat(1, 1fr);
 gap: 20px;
}

.brand-card {
 height: 211px;
 border-radius: 10px;
 position: relative;
 color: white;
 overflow: hidden;
 perspective: 1500px;
}

.brand-card-number {
 position: absolute;
 top: 30px;
 left: 30px;
 font-family: "Playfair Display", serif;
 font-style: italic;
 font-size: 25px;
 line-height: 0.95em;
 font-weight: 400;
}

.brand-card-title {
 position: absolute;
 bottom: 50px;
 left: 30px;
 font-size: 50px;
 font-family: "Playfair Display", serif;
 line-height: 1em;
}

.brand-card-separator {
 position: absolute;
 bottom: 35px;
 left: 30px;
 width: calc(100% - 60px);
 height: 1px;
 background-color: rgba(255, 255, 255, 0.5);
}

.brand-conclusion-text {
 font-size: 20px;
 line-height: 26px;
}

.brand-conclusion-text p {
 margin-bottom: 20px;
}

/* Banner Section */
.brand-banner {
 width: 100%;
 max-width: 1440px;
 height: 380px;
 background-color: #d9d9d9;
 display: flex;
 align-items: center;
 justify-content: center;
 margin: 0 auto;
}

.brand-banner-text {
 font-family: "Raleway";
 font-weight: 600;
 font-size: 35px;
 color: white;
}

/* Flip Card Styles */
.brand-flip-card {
 background-color: transparent;
 cursor: pointer;
 transform-style: preserve-3d;
}

.brand-flip-card-inner {
 position: relative;
 width: 100%;
 height: 100%;
 text-align: left;
 transition: transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);
 transform-style: preserve-3d;
}

.brand-flip-card.flipped .brand-flip-card-inner {
 transform: rotateY(180deg);
}

.brand-flip-card-front,
.brand-flip-card-back {
 position: absolute;
 width: 100%;
 height: 100%;
 -webkit-backface-visibility: hidden;
 backface-visibility: hidden;
 border-radius: 10px;
}

.brand-flip-card-front {
 background-color: transparent;
}

.brand-flip-card-back {
 transform: rotateY(180deg);
 padding: 30px;
 display: flex;
 flex-direction: column;
 justify-content: flex-start;
}

.brand-back-content {
 height: 100%;
 display: flex;
 flex-direction: column;
 gap: 15px;
}

.brand-back-title {
 font-family: "Playfair Display";
 font-size: 25px;
 line-height: 0.95em;
 font-weight: 400;
 color: #ffffff;
}

.brand-back-description {
 font-family: "Raleway";
 font-size: 26px;
 line-height: 1.23em;
 font-weight: 400;
 color: #ffffff;
 white-space: pre-line;
}

.content {
 display: flex;
 flex-direction: column;
 gap: 40px;
 width: 100%;
 padding-bottom: 180px;
}

.content-header {
 display: flex;
 flex-direction: column;
 gap: 14px;
 width: 100%;
 margin-bottom: 40px;
}

.content-title {
 font-family: "Playfair Display", serif;
 font-weight: 400;
 font-size: 60px;
 line-height: 0.95em;
 color: #292b33;
 text-transform: lowercase;
}

.content-divider {
 width: 100%;
 height: 3px;
 background-color: #2d363d;
}

.content-section {
 display: flex;
 flex-direction: column;
 gap: 15px;
 width: 100%;
 margin-bottom: 40px;
}

.section-header {
 display: flex;
 align-items: center;
 gap: 15px;
 width: 100%;
 h3 {
  margin-bottom: 0 !important;
 }
}

.section-title {
 font-family: "Raleway", sans-serif;
 font-weight: 600;
 font-size: 20px;
 line-height: 0.95em;
 color: #6b6f70;
 white-space: nowrap;
}

.section-divider {
 flex: 1;
 height: 2px;
 background-color: #6b6f70;
}

.section-content {
 display: flex;
 flex-direction: column;
 gap: 28px;
 width: 100%;
}

.section-content {
 row-gap: 36px;
}

.section-content-grid {
 display: flex;
 flex-direction: column;
 gap: 36px;
}

.section-left {
 display: flex;
 flex-direction: column;
 gap: 36px;
}

.section-right {
 display: flex;
 flex-direction: column;
 gap: 36px;
}

.content-item {
 display: flex;
 justify-content: space-between;
 align-items: center;
 gap: 12px;
 width: 100%;
}

.item-text {
 display: flex;
 flex-direction: column;
 gap: 6px;
 flex: 1;
}

.item-title {
 font-family: "Playfair Display", serif;
 font-weight: 700;
 font-size: 20px;
 line-height: 1.33em;
 color: #292b33;
 margin-bottom: 0;
}

.item-subtitle {
 font-family: "Raleway", sans-serif;
 font-weight: 400;
 font-size: 18px !important;
 line-height: 1.17em;
 color: #292b33;
 margin: 0;
}

.item-number {
 font-family: "Raleway", sans-serif;
 font-weight: 700;
 font-size: 19.65px;
 line-height: 1.17em;
 color: #292b33;
 white-space: nowrap;
}

.journey-section .section-content {
 display: flex;
 flex-direction: column;
 gap: 20px;
 width: 100%;
}

.journey-item {
 display: flex;
 justify-content: space-between;
 align-items: center;
 gap: 12px;
 width: 100%;
}

.journey-item .item-text {
 flex: 1;
 min-width: 0; /* Allows text to wrap properly */
}

/* Responsive Design */
@media (min-width: 768px) {
 .brand-journey-cards {
  grid-template-columns: repeat(2, 1fr);
 }
}

@media (min-width: 1024px) {
 .brand-journey-cards {
  grid-template-columns: repeat(3, 1fr);
 }
}

/* Brand Embed Styles */
#brand-embed {
 background-color: white;
 overflow-y: auto;
 max-height: 100vh;
 padding: 30px;
 box-sizing: border-box;
 outline: none; /* Remove focus outline */
 -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
}

#brand-embed .brand-hero {
 padding: 150px 0;
}

#brand-embed .brand-hero-text {
 font-family: "Playfair Display", serif;
 font-size: 50px;
 line-height: 70px;
 text-align: center;
 color: #333;
}

#brand-embed .brand-journey {
 padding: 50px 0;
}

#brand-embed .brand-journey-content {
 max-width: 1141px;
 margin: 0 auto;
 display: flex;
 flex-direction: column;
 gap: 50px;
 padding: 0 20px;
}

#brand-embed .brand-intro-text {
 font-size: 22px;
 line-height: 1.5;
 color: #333;
}

#brand-embed .brand-intro-text p {
 margin-bottom: 20px;
}

#brand-embed .brand-journey-cards {
 display: grid;
 grid-template-columns: repeat(1, 1fr);
 gap: 20px;
}

#brand-embed .brand-card {
 height: 211px;
 border-radius: 10px;
 position: relative;
 color: white;
 overflow: hidden;
 perspective: 1500px;
}

/* Media queries for responsive grid */
@media (min-width: 768px) {
 #brand-embed .brand-journey-cards {
  grid-template-columns: repeat(2, 1fr);
 }
}

@media (min-width: 1024px) {
 #brand-embed .brand-journey-cards {
  grid-template-columns: repeat(3, 1fr);
 }
}

/* Brand Card Specific Styles in Embed */
#brand-embed .brand-card-number {
 position: absolute;
 top: 30px;
 left: 30px;
 font-family: "Playfair Display", serif;
 font-style: italic;
 font-size: 25px;
 line-height: 0.95em;
 font-weight: 400;
}

#brand-embed .brand-card-title {
 position: absolute;
 bottom: 50px;
 left: 30px;
 font-size: 50px;
 font-family: "Playfair Display", serif;
 line-height: 1em;
}

#brand-embed .brand-card-separator {
 position: absolute;
 bottom: 35px;
 left: 30px;
 width: calc(100% - 60px);
 height: 1px;
 background-color: rgba(255, 255, 255, 0.5);
}

/* Brand Flip Card Styles in Embed */
#brand-embed .brand-flip-card {
 background-color: transparent;
 cursor: pointer;
 transform-style: preserve-3d;
}

#brand-embed .brand-flip-card-inner {
 position: relative;
 width: 100%;
 height: 100%;
 text-align: left;
 transition: transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);
 transform-style: preserve-3d;
}

#brand-embed .brand-flip-card.flipped .brand-flip-card-inner {
 transform: rotateY(180deg);
}

#brand-embed .brand-flip-card-front,
#brand-embed .brand-flip-card-back {
 position: absolute;
 width: 100%;
 height: 100%;
 -webkit-backface-visibility: hidden;
 backface-visibility: hidden;
 border-radius: 10px;
}

#brand-embed .brand-flip-card-front {
 background-color: transparent;
}

#brand-embed .brand-flip-card-back {
 transform: rotateY(180deg);
 padding: 30px;
 display: flex;
 flex-direction: column;
 justify-content: flex-start;
}

#brand-embed .brand-back-content {
 height: 100%;
 display: flex;
 flex-direction: column;
 gap: 15px;
}

#brand-embed .brand-back-title {
 font-family: "Playfair Display", serif;
 font-size: 25px;
 line-height: 0.95em;
 font-weight: 400;
 color: #ffffff;
}

#brand-embed .brand-back-description {
 font-family: "Raleway";
 font-size: 26px;
 line-height: 1.23em;
 font-weight: 400;
 color: #ffffff;
 white-space: pre-line;
}

/* Brand Embed Fullscreen */
#brand-embed-fullscreen {
 width: 100%;
 background-color: white;
 padding: 20px;
 box-sizing: border-box;
 overflow-y: auto;
 position: relative;
 z-index: 999;
 min-height: 100vh;
}

#brand-embed-fullscreen .brand-hero {
 padding: 100px 0;
 margin: 0 auto;
 max-width: 1280px;
}

#brand-embed-fullscreen .brand-journey {
 padding: 50px 0;
 margin: 0 auto;
 max-width: 1280px;
}

#brand-embed-fullscreen .brand-banner {
 margin: 50px auto;
 max-width: 1280px;
}

/* Journey Steps */
.journey-steps .step {
 cursor: pointer;
}

/* Journey Steps */
.journey-steps .step:hover {
 transform: translateY(-5px);
}

.journey-steps .step:hover .step-title {
 color: #0056b3;
}

.journey-steps .step:after {
 content: "";
 position: absolute;
 bottom: -10px;
 left: 0;
 width: 0;
 height: 2px;
 background-color: #0056b3;
 transition: width 0.3s ease;
}

.journey-steps .step:hover:after {
 width: 100%;
}

.loyalty-bottom-section .container {
 max-width: 1140px;
 margin: 0 auto;
}

/* Update animation styles */
.loyalty-badge-content .animationText,
.loyalty-bottom-section .animationText {
 transition: opacity 0.8s ease, transform 0.8s ease;
}

/* Only apply these styles after animation completes */
.loyalty-badge-content .animationText.animated,
.loyalty-bottom-section .animationText.animated {
 opacity: 1;
 transform: translateY(0);
}

/* Keep the hover effects */
.loyalty-bottom-title:hover {
 color: #000000;
}

@media (min-width: 1024px) {
 .content-title {
  font-size: 100px;
 }

 .container {
  width: 100%;
  max-width: 1240px;
  margin: 0 auto;
  padding: 0;
 }

 .section-header {
  width: 100%;
 }

 .content-section {
  gap: 28px;
 }

 .section-content {
  width: 49%;
  row-gap: 49px;
 }

 .section-content-grid {
  display: flex;
  flex-direction: row;
 }

 .section-left {
  display: flex;
  flex-direction: column;
  width: 51%;
  row-gap: 49px;
 }

 .section-right {
  display: flex;
  flex-direction: column;
  width: 50%;
  row-gap: 49px;
  padding-left: 27px;
 }

 .journey-section .section-content {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 27px;
  width: 100%;
  margin: 0;
 }

 .journey-item {
  gap: 37px;
 }

 .journey-item .item-text {
  flex: 1;
  min-width: 0;
 }

 .content-item {
  width: 100%;
 }
}

/* Container styles */
.container {
 width: 100%;
 max-width: 1240px;
 margin: 0 auto;
 padding: 0 20px;
 box-sizing: border-box;
}

/* Tablet styles */
@media (min-width: 768px) and (max-width: 1023px) {
 .content-title {
  font-size: 80px;
 }
}
