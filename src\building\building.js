// This file is included for potential future enhancements
// Currently, the text reveal animation is handled by CSS

document.addEventListener('DOMContentLoaded', () => {
    const floatingImages = document.querySelectorAll('.floating-image');
    const revealTexts = document.querySelectorAll('.reveal-text');
    let isImagesVisible = false;
    let isAnimatingBack = false;
    let wheelTimeout = null;
    let isAnimating = false;

    // Initial text animation
    startTextAnimation();

    function startTextAnimation() {
        revealTexts.forEach(text => {
            text.classList.add('animate');
        });
    }

    function resetTextAnimation() {
        revealTexts.forEach(text => {
            text.classList.remove('animate');
            // Force reflow
            void text.offsetWidth;
            text.classList.add('animate');
        });
    }

    // Handle wheel events for animation
    window.addEventListener('wheel', (e) => {
        if (isAnimating) return;

        clearTimeout(wheelTimeout);
        wheelTimeout = setTimeout(() => {
            const scrollingDown = e.deltaY > 0;

            if (scrollingDown && !isImagesVisible) {
                isAnimating = true;
                showImages();
                isImagesVisible = true;
                isAnimatingBack = false;
                setTimeout(() => {
                    isAnimating = false;
                }, 1000); // Prevent new animations while current one is playing
            } else if (!scrollingDown && isImagesVisible) {
                isAnimating = true;
                hideImages();
                isImagesVisible = false;
                if (!isAnimatingBack) {
                    isAnimatingBack = true;
                    setTimeout(() => {
                        resetTextAnimation();
                        isAnimating = false;
                    }, 800);
                }
            } else {
                isAnimating = false;
            }
        }, 50); // Debounce wheel events
    });

    function showImages() {
        floatingImages.forEach((img, index) => {
            setTimeout(() => {
                img.classList.add('visible');
            }, index * 200);
        });
    }

    function hideImages() {
        floatingImages.forEach(img => {
            img.classList.remove('visible');
        });
    }

    // Smooth scroll to top when images are hidden
    function scrollToTop() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    }
}); 