document.addEventListener("DOMContentLoaded", () => {
 if (typeof gsap !== "undefined" && typeof ScrollTrigger !== "undefined") {
  gsap.registerPlugin(ScrollTrigger);

  initSlideInAnimations();
 } else {
  console.error("GSAP or ScrollTrigger not loaded");
 }
});


function initSlideInAnimations() {
 // Get all animation elements
 const allAnimationElements = document.querySelectorAll(".animationText");
 
 // First pass: Set initial state for all animated elements
 allAnimationElements.forEach((element) => {
   gsap.set(element, {
     opacity: 0,
     y: 50,
   });
 });
 
 // Second pass: Create animations for all elements
 allAnimationElements.forEach((element) => {
   // Check if it's a special element we want custom animation for
   const isSpecialElement = 
     element.classList.contains('loyalty-percentage') || 
     element.classList.contains('loyalty-text') || 
     element.classList.contains('loyalty-bottom-title') || 
     element.classList.contains('loyalty-bottom-subtitle') || 
     element.classList.contains('loyalty-arrow-icon');
   
   if (isSpecialElement) {
     // Handle these elements with special animations
     gsap.to(element, {
       opacity: 1,
       y: 0,
       duration: 0.8,
       ease: "power2.out",
       scrollTrigger: {
         trigger: element.closest('.loyalty-content-section') || element.closest('.loyalty-bottom-section'),
         start: "top 80%",
         toggleActions: "play none none none",
         once: true,
       },
       onComplete: function() {
         element.classList.add('animated');
         // Clear inline styles after animation
         setTimeout(() => {
           element.style.transform = '';
           element.style.opacity = '';
         }, 100);
       }
     });
   } else {
     // Regular animation for other elements
     gsap.to(element, {
       opacity: 1,
       y: 0,
       duration: 0.8,
       ease: "power2.out",
       scrollTrigger: {
         trigger: element,
         start: "top 80%",
         toggleActions: "play none none none",
         once: true,
       },
       onComplete: function() {
         element.classList.add('animated');
       }
     });
   }
 });
 
 // Add specific staggered animation for the badge elements
 const badgeElements = document.querySelectorAll('.loyalty-percentage, .loyalty-text');
 if (badgeElements.length) {
   gsap.to(badgeElements, {
     opacity: 1,
     y: 0,
     duration: 0.5,
     stagger: 0.15,
     ease: "power2.out",
     scrollTrigger: {
       trigger: '.loyalty-statistic-badge',
       start: "top 80%",
       toggleActions: "play none none none",
       once: true,
     }
   });
 }
 
 // Add specific staggered animation for the bottom section elements
 const bottomSectionElements = document.querySelectorAll('.loyalty-bottom-title, .loyalty-bottom-subtitle, .loyalty-arrow-icon');
 if (bottomSectionElements.length) {
   gsap.to(bottomSectionElements, {
     opacity: 1,
     y: 0,
     duration: 0.5,
     stagger: 0.15,
     ease: "power2.out",
     scrollTrigger: {
       trigger: '.loyalty-bottom-section',
       start: "top 80%",
       toggleActions: "play none none none",
       once: true,
     }
   });
 }
}

/**
 * Apply slide-in animation to specific elements
 * @param {string} selector - CSS selector for the elements to animate
 * @param {Object} options - Optional configuration options
 */
function applySlideInAnimation(selector, options = {}) {
 const elements = document.querySelectorAll(selector);

 if (elements.length === 0) {
  console.warn(`No elements found for selector: ${selector}`);
  return;
 }

 const defaults = {
  duration: 0.8,
  delay: 0,
  y: 50,
  stagger: 0.1,
  start: "top 80%",
  ease: "power2.out",
 };

 
 const config = { ...defaults, ...options };


 elements.forEach((element) => {
  gsap.set(element, {
   opacity: 0,
   y: config.y,
  });

  gsap.to(element, {
   opacity: 1,
   y: 0,
   duration: config.duration,
   delay: config.delay,
   ease: config.ease,
   scrollTrigger: {
    trigger: element,
    start: config.start,
    toggleActions: "play none none none",
    once: true,
   },
  });
 });
}

window.initSlideInAnimations = initSlideInAnimations;
window.applySlideInAnimation = applySlideInAnimation;
