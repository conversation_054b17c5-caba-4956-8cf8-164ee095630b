// Brand benefits data
const brandBenefits = [
    "Competitive edge",
    "Cost effective",
    "Increased lifetime value and purchase frequency",
    "Brand consistency",
    "Stronger brand advocacy"
];

// Initialize benefits list
function initializeBenefits() {
    const benefitsList = document.getElementById('benefitsList');
    
    brandBenefits.forEach(benefit => {
        const benefitItem = document.createElement('div');
        benefitItem.className = 'benefit-item';
        
        benefitItem.innerHTML = `
            <h3 class="benefit-title">${benefit}</h3>
            <span class="benefit-toggle">v</span>
        `;
        
        benefitsList.appendChild(benefitItem);
    });
}

// Mobile menu functionality
function initializeMobileMenu() {
    const menuBtn = document.getElementById('mobileMenuBtn');
    
    menuBtn.addEventListener('click', () => {
        // Add your mobile menu functionality here
        console.log('Mobile menu clicked');
    });
}

// Initialize everything when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    initializeBenefits();
    initializeMobileMenu();
});