document.addEventListener('DOMContentLoaded', function() {
    // Hamburger menu functionality
    const hamburgerMenu = document.querySelector('.hamburger-menu');
    
    hamburgerMenu.addEventListener('click', function() {
      // Add your menu toggle logic here
      console.log('Menu clicked');
    });
  
    // Slide functionality
    const slides = document.querySelectorAll('.slide');
    const nextButton = document.querySelector('.next-button');
    let currentSlide = 1;
    const totalSlides = slides.length;
  
    // Initialize the first slide as active on page load
    slides[0].classList.add('active');
  
    // Function to navigate to a specific slide
    function goToSlide(slideNumber) {
      // Remove all classes first
      slides.forEach(slide => {
        slide.classList.remove('active', 'previous');
      });
      
      // Add active class to current slide
      const targetSlide = document.querySelector(`.slide[data-slide="${slideNumber}"]`);
      
      // Get previous slide
      const prevSlideNumber = slideNumber === 1 ? totalSlides : slideNumber - 1;
      const prevSlide = document.querySelector(`.slide[data-slide="${prevSlideNumber}"]`);
      
      prevSlide.classList.add('previous');
      targetSlide.classList.add('active');
      
      currentSlide = slideNumber;
    }
  
    // Next button click handler
    nextButton.addEventListener('click', function() {
      const nextSlide = currentSlide === totalSlides ? 1 : currentSlide + 1;
      goToSlide(nextSlide);
    });
  
    // Add smooth scroll for better user experience
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        document.querySelector(this.getAttribute('href')).scrollIntoView({
          behavior: 'smooth'
        });
      });
    });
  });