document.addEventListener('DOMContentLoaded', function() {
    // Intersection Observer for section animations
    const sections = document.querySelectorAll('.section');
    
    const sectionObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('visible');
        }
      });
    }, {
      threshold: 0.1
    });
  
    sections.forEach(section => {
      sectionObserver.observe(section);
    });
  
    // Initialize star badges
    function initializeStarBadges() {
      const starBadges = document.querySelectorAll('.star-badge');
      starBadges.forEach(badge => {
        const percentage = badge.dataset.percentage;
        const text = badge.dataset.text;
        
        badge.innerHTML = `
          <div class="star-content">
            <span class="percentage">${percentage}%</span>
            <span class="text">${text}</span>
          </div>
        `;
      });
    }
  
    // Initialize cards
    function initializeCards() {
      const cards = document.querySelectorAll('.card');
      cards.forEach(card => {
        card.addEventListener('click', function() {
          this.classList.toggle('expanded');
        });
      });
    }
  
    // Handle smooth scrolling
    function initializeSmoothScroll() {
      document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
          e.preventDefault();
          const target = document.querySelector(this.getAttribute('href'));
          if (target) {
            target.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
            });
          }
        });
      });
    }
  
    // Handle image loading
    function handleImageLoading() {
      const images = document.querySelectorAll('img');
      images.forEach(img => {
        img.addEventListener('load', function() {
          this.classList.add('loaded');
        });
        
        img.addEventListener('error', function() {
          this.src = 'placeholder.png'; // Fallback image
        });
      });
    }
  
    // Initialize all functionality
    initializeStarBadges();
    initializeCards();
    initializeSmoothScroll();
    handleImageLoading();
  });