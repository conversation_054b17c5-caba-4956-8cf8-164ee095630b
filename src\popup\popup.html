<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Form Page</title>
    <link rel="stylesheet" href="popup.css" />
    <link rel="stylesheet" href="styles/texts.css" />
  </head>
  <body>
    <div class="form-container">
      <button class="close-button">
        <img
          src="../public/close-icon.svg"
          alt="Close"
          width="22"
          height="22"
        />
      </button>

      <div class="form-content">
        <header class="form-header">
          <h1>So glad to see you here!</h1>
          <p>
            Please fill in the below to unlock the rest of the content - we
            promise it's worth it!
          </p>
        </header>

        <form id="custom-form" class="form">
          <div class="form-fields">
            <!-- Name and Surname fields -->
            <div class="form-row">
              <div class="form-group">
                <label>Name *</label>
                <input type="text" id="name" name="name" />
                <p class="error-message" id="nameError"></p>
              </div>
              <div class="form-group">
                <label>Surname *</label>
                <input type="text" id="surname" name="surname" />
                <p class="error-message" id="surnameError"></p>
              </div>
            </div>

            <!-- Email field -->
            <div class="form-group full-width">
              <label>Email *</label>
              <input type="email" id="email" name="email" />
              <p class="error-message" id="emailError"></p>
            </div>

            <!-- Company and Role fields -->
            <div class="form-row">
              <div class="form-group">
                <label>Company</label>
                <input type="text" id="company" name="company" />
              </div>
              <div class="form-group">
                <label>Role</label>
                <input type="text" id="role" name="role" />
              </div>
            </div>

            <!-- Origin field - hidden, will be set automatically -->
            <input
              type="hidden"
              id="origin"
              name="origin"
              value="Customer Engagement"
            />
          </div>

          <!-- Checkboxes -->
          <div class="checkbox-group">
            <div class="checkbox-item">
              <input
                type="checkbox"
                id="privacy_policy"
                name="privacy_policy"
              />
              <label for="privacy_policy" id="privacyLabel">
                I agree to Motivait's
                <span class="underline">Privacy Policy</span> and I accept the
                use of my data to respond to my request. *
              </label>
            </div>
            <p class="error-message" id="privacyError"></p>

            <div class="checkbox-item">
              <input
                type="checkbox"
                id="receive_more_info"
                name="receive_more_info"
              />
              <label for="receive_more_info">
                Opt in to receive further information, insights, updates, and
                more from Motivait.
              </label>
            </div>
          </div>

          <!-- Legal text -->
          <p class="legal-text">
            According to the new data protection legislation (Regulation UE
            2016/679 General of Protection of Data or GDPR) we inform you that
            we will always collect your data with your consent and only to
            answer your query and that we will save your data a maximum of six
            months unless you become a customer. We also inform you that you can
            exercise your rights of access, rectification, deletion, limitation
            and <NAME_EMAIL> For more information, please
            consult our privacy policy.
          </p>

          <!-- Submit button -->
          <div class="submit-button-container">
            <button type="submit" class="submit-button">Send</button>
          </div>
        </form>
      </div>
    </div>

    <script src="popup.js"></script>
  </body>
</html>
